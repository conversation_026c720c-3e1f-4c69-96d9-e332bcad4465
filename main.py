#!/usr/bin/env python3
"""
Typewriter Overlay - A typing practice overlay for Windows
Features:
- Overlay that works above fullscreen applications
- Words scroll from center to left as typed
- Global hotkeys for show/hide and statistics
- Session management with configurable timeout
- Resource efficient implementation
"""

import tkinter as tk
import time
import threading
import sys
import os

# Import our modules
from config import Config
from word_list import WordList
from overlay_window import OverlayWindow
from text_animator import TextAnimator
from input_handler import InputHandler, SessionManager
from statistics_display import StatisticsDisplay, StatisticsTracker
from hotkey_manager import SimpleHotkeyManager

class TypewriterOverlay:
    def __init__(self):
        self.config = Config()
        self.word_list = WordList()
        self.session_manager = SessionManager(self.config)
        self.statistics_tracker = StatisticsTracker()
        
        # UI components
        self.overlay_window = None
        self.text_animator = None
        self.statistics_display = None
        self.input_handler = None
        self.hotkey_manager = None
        
        # State
        self.is_running = False
        self.main_thread = None
        self.update_thread = None
        
    def initialize(self):
        """Initialize all components"""
        try:
            # Create overlay window
            self.overlay_window = OverlayWindow(self.config)
            root = self.overlay_window.create_window()
            
            # Create text animator
            self.text_animator = TextAnimator(self.overlay_window, self.config)
            
            # Create statistics display
            self.statistics_display = StatisticsDisplay(self.config, self.session_manager)
            self.statistics_display.create_window()
            
            # Create input handler
            self.input_handler = InputHandler(self.text_animator, self.session_manager)
            
            # Create hotkey manager
            self.hotkey_manager = SimpleHotkeyManager(self.config)
            self.hotkey_manager.register_toggle_callback(self.toggle_overlay)
            self.hotkey_manager.register_stats_callback(self.toggle_statistics)
            
            return True
            
        except Exception as e:
            print(f"Error initializing application: {e}")
            return False
    
    def start(self):
        """Start the application"""
        if not self.initialize():
            return False
        
        self.is_running = True
        
        # Start hotkey manager
        self.hotkey_manager.start()
        
        # Start input handler
        self.input_handler.start_listening()
        
        # Start update thread
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
        
        print("Typewriter Overlay started!")
        print(f"Toggle overlay: {self.config.get('toggle_hotkey')}")
        print(f"Show statistics: {self.config.get('stats_hotkey')}")
        print("Press Ctrl+C to exit")
        
        # Keep main thread alive
        try:
            while self.is_running:
                if self.overlay_window and self.overlay_window.root:
                    self.overlay_window.update()
                time.sleep(0.01)  # 10ms update cycle
        except KeyboardInterrupt:
            print("\nShutting down...")
            self.stop()
        
        return True
    
    def stop(self):
        """Stop the application"""
        self.is_running = False
        
        # Stop components
        if self.hotkey_manager:
            self.hotkey_manager.stop()
        
        if self.input_handler:
            self.input_handler.stop_listening()
        
        # Destroy windows
        if self.statistics_display:
            self.statistics_display.destroy()
        
        if self.overlay_window:
            self.overlay_window.destroy()
    
    def toggle_overlay(self):
        """Toggle the overlay visibility"""
        if not self.overlay_window:
            return
        
        if self.overlay_window.is_visible:
            # Hide overlay
            self.overlay_window.hide()
            self.input_handler.deactivate()
        else:
            # Show overlay and start new session
            self.start_new_session()
            self.overlay_window.show()
            self.input_handler.activate()
    
    def toggle_statistics(self):
        """Toggle the statistics display"""
        if self.statistics_display:
            self.statistics_display.toggle_visibility()
    
    def start_new_session(self):
        """Start a new typing session"""
        # Generate new words
        words = self.word_list.generate_word_sequence(
            self.config.get('words_per_line', 10)
        )
        
        # Set words in text animator
        self.text_animator.set_words(words)
        
        # Reset session manager
        if self.session_manager.is_session_active():
            # Save current session to tracker
            stats = self.session_manager.get_statistics()
            self.statistics_tracker.add_session(stats)
            self.session_manager.end_session()
    
    def _update_loop(self):
        """Main update loop running in separate thread"""
        while self.is_running:
            try:
                # Update statistics display if visible
                if (self.statistics_display and 
                    self.statistics_display.is_visible):
                    self.statistics_display.update()
                
                # Check for session timeout
                if (self.session_manager.is_session_active() and
                    self.input_handler.is_active):
                    
                    timeout = self.config.get('idle_timeout', 3.0)
                    if not self.input_handler.is_input_recent(timeout):
                        # Session timed out
                        stats = self.session_manager.get_statistics()
                        self.statistics_tracker.add_session(stats)
                        self.session_manager.end_session()
                        
                        # Hide overlay
                        if self.overlay_window:
                            self.overlay_window.hide()
                        self.input_handler.deactivate()
                
                time.sleep(0.1)  # Update every 100ms
                
            except Exception as e:
                print(f"Error in update loop: {e}")
                time.sleep(1)  # Wait longer on error


def main():
    """Main entry point"""
    print("Starting Typewriter Overlay...")
    
    # Check if running on Windows
    if os.name != 'nt':
        print("This application is designed for Windows only.")
        return 1
    
    # Create and start application
    app = TypewriterOverlay()
    
    try:
        success = app.start()
        return 0 if success else 1
    except Exception as e:
        print(f"Fatal error: {e}")
        return 1
    finally:
        app.stop()


if __name__ == "__main__":
    sys.exit(main())
