# Word list management for typing practice
import random

class WordList:
    def __init__(self):
        # Common English words for typing practice
        self.words = [
            "the", "and", "for", "are", "but", "not", "you", "all", "can", "had", "her", "was", "one", "our", "out", "day", "get", "has", "him", "his", "how", "man", "new", "now", "old", "see", "two", "way", "who", "boy", "did", "its", "let", "put", "say", "she", "too", "use",
            "about", "after", "again", "before", "being", "could", "every", "first", "found", "great", "group", "house", "large", "last", "left", "life", "little", "long", "made", "make", "many", "most", "move", "much", "name", "never", "next", "night", "number", "other", "part", "place", "point", "right", "same", "seem", "small", "sound", "still", "such", "take", "than", "that", "their", "there", "these", "they", "thing", "think", "this", "those", "three", "time", "very", "want", "water", "well", "went", "were", "what", "where", "which", "while", "with", "work", "world", "would", "write", "year", "young",
            "against", "another", "around", "because", "become", "between", "change", "children", "come", "country", "different", "does", "each", "example", "follow", "good", "help", "here", "home", "important", "into", "just", "know", "learn", "line", "live", "look", "means", "might", "need", "only", "over", "own", "people", "play", "school", "should", "show", "small", "some", "start", "state", "system", "turn", "under", "until", "used", "want", "ways", "when", "will", "without", "word", "work"
        ]
        self.current_words = []
        self.current_index = 0
        
    def generate_word_sequence(self, count=10):
        """Generate a sequence of random words"""
        self.current_words = random.sample(self.words, min(count, len(self.words)))
        self.current_index = 0
        return self.current_words
    
    def get_current_word(self):
        """Get the current word to type"""
        if self.current_index < len(self.current_words):
            return self.current_words[self.current_index]
        return None
    
    def next_word(self):
        """Move to the next word"""
        self.current_index += 1
        return self.get_current_word()
    
    def get_remaining_words(self):
        """Get all remaining words in the sequence"""
        return self.current_words[self.current_index:]
    
    def reset(self):
        """Reset to the beginning of the current sequence"""
        self.current_index = 0
