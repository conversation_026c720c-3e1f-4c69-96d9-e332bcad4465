use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mute<PERSON>;
use tokio::time::sleep;

use crate::config::Config;

#[derive(Debug, <PERSON>lone)]
pub struct SessionStatistics {
    pub active: bool,
    pub duration: f64,
    pub wpm: f64,
    pub accuracy: f64,
    pub total_keystrokes: u32,
    pub correct_keystrokes: u32,
    pub incorrect_keystrokes: u32,
    pub backspaces: u32,
    pub words_completed: u32,
}

impl Default for SessionStatistics {
    fn default() -> Self {
        SessionStatistics {
            active: false,
            duration: 0.0,
            wpm: 0.0,
            accuracy: 0.0,
            total_keystrokes: 0,
            correct_keystrokes: 0,
            incorrect_keystrokes: 0,
            backspaces: 0,
            words_completed: 0,
        }
    }
}

pub struct SessionManager {
    config: Arc<Config>,
    session_active: bool,
    session_start_time: Option<Instant>,
    last_activity_time: Option<Instant>,
    total_keystrokes: u32,
    correct_keystrokes: u32,
    incorrect_keystrokes: u32,
    backspaces: u32,
    words_completed: u32,
    should_monitor: bool,
    timeout_handle: Option<tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON><()>>,
}

impl SessionManager {
    pub fn new(config: Arc<Config>) -> Self {
        SessionManager {
            config,
            session_active: false,
            session_start_time: None,
            last_activity_time: None,
            total_keystrokes: 0,
            correct_keystrokes: 0,
            incorrect_keystrokes: 0,
            backspaces: 0,
            words_completed: 0,
            should_monitor: false,
            timeout_handle: None,
        }
    }
    
    pub fn start_session(&mut self) {
        if !self.session_active {
            self.session_active = true;
            let now = Instant::now();
            self.session_start_time = Some(now);
            self.last_activity_time = Some(now);
            
            // Reset statistics
            self.total_keystrokes = 0;
            self.correct_keystrokes = 0;
            self.incorrect_keystrokes = 0;
            self.backspaces = 0;
            self.words_completed = 0;
            
            self.start_timeout_monitor();
        }
    }
    
    pub fn end_session(&mut self) {
        if self.session_active {
            self.session_active = false;
            self.session_start_time = None;
            self.last_activity_time = None;
            self.stop_timeout_monitor();
        }
    }
    
    pub fn is_session_active(&self) -> bool {
        self.session_active
    }
    
    pub fn update_activity(&mut self) {
        if self.session_active {
            self.last_activity_time = Some(Instant::now());
        } else {
            self.start_session();
        }
    }
    
    pub fn add_keystroke(&mut self, _character: char, is_correct: bool) {
        if !self.session_active {
            self.start_session();
        }
        
        self.update_activity();
        self.total_keystrokes += 1;
        
        if is_correct {
            self.correct_keystrokes += 1;
        } else {
            self.incorrect_keystrokes += 1;
        }
    }
    
    pub fn add_backspace(&mut self) {
        if self.session_active {
            self.update_activity();
            self.backspaces += 1;
        }
    }
    
    pub fn add_word_completed(&mut self) {
        if self.session_active {
            self.update_activity();
            self.words_completed += 1;
        }
    }
    
    pub fn get_session_duration(&self) -> f64 {
        if let Some(start_time) = self.session_start_time {
            start_time.elapsed().as_secs_f64()
        } else {
            0.0
        }
    }
    
    pub fn get_wpm(&self) -> f64 {
        let duration_minutes = self.get_session_duration() / 60.0;
        if duration_minutes > 0.0 {
            // Standard WPM calculation: (characters typed / 5) / minutes
            // We use correct keystrokes as a proxy for characters
            (self.correct_keystrokes as f64 / 5.0) / duration_minutes
        } else {
            0.0
        }
    }
    
    pub fn get_accuracy(&self) -> f64 {
        if self.total_keystrokes > 0 {
            (self.correct_keystrokes as f64 / self.total_keystrokes as f64) * 100.0
        } else {
            0.0
        }
    }
    
    pub fn get_statistics(&self) -> SessionStatistics {
        SessionStatistics {
            active: self.session_active,
            duration: self.get_session_duration(),
            wpm: self.get_wpm(),
            accuracy: self.get_accuracy(),
            total_keystrokes: self.total_keystrokes,
            correct_keystrokes: self.correct_keystrokes,
            incorrect_keystrokes: self.incorrect_keystrokes,
            backspaces: self.backspaces,
            words_completed: self.words_completed,
        }
    }
    
    fn start_timeout_monitor(&mut self) {
        if self.timeout_handle.is_some() {
            return;
        }
        
        self.should_monitor = true;
        let config = self.config.clone();
        let timeout_duration = Duration::from_secs_f64(config.idle_timeout);
        
        // Note: In a real implementation, we'd need to share state properly
        // This is a simplified version for demonstration
        let handle = tokio::spawn(async move {
            loop {
                sleep(Duration::from_millis(100)).await;
                // In a real implementation, we'd check if timeout has occurred
                // and call end_session() if needed
            }
        });
        
        self.timeout_handle = Some(handle);
    }
    
    fn stop_timeout_monitor(&mut self) {
        self.should_monitor = false;
        if let Some(handle) = self.timeout_handle.take() {
            handle.abort();
        }
    }
    
    pub fn check_timeout(&mut self) -> bool {
        if !self.session_active {
            return false;
        }
        
        if let Some(last_activity) = self.last_activity_time {
            let timeout_duration = Duration::from_secs_f64(self.config.idle_timeout);
            if last_activity.elapsed() > timeout_duration {
                self.end_session();
                return true;
            }
        }
        
        false
    }
}

impl Drop for SessionManager {
    fn drop(&mut self) {
        self.stop_timeout_monitor();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    
    #[tokio::test]
    async fn test_session_lifecycle() {
        let config = Arc::new(Config::default());
        let mut session_manager = SessionManager::new(config);
        
        assert!(!session_manager.is_session_active());
        
        session_manager.start_session();
        assert!(session_manager.is_session_active());
        
        session_manager.end_session();
        assert!(!session_manager.is_session_active());
    }
    
    #[tokio::test]
    async fn test_keystroke_tracking() {
        let config = Arc::new(Config::default());
        let mut session_manager = SessionManager::new(config);
        
        session_manager.add_keystroke('a', true);
        session_manager.add_keystroke('b', false);
        session_manager.add_backspace();
        
        let stats = session_manager.get_statistics();
        assert_eq!(stats.total_keystrokes, 2);
        assert_eq!(stats.correct_keystrokes, 1);
        assert_eq!(stats.incorrect_keystrokes, 1);
        assert_eq!(stats.backspaces, 1);
    }
    
    #[tokio::test]
    async fn test_wpm_calculation() {
        let config = Arc::new(Config::default());
        let mut session_manager = SessionManager::new(config);
        
        session_manager.start_session();
        
        // Simulate typing 25 correct characters (5 words)
        for _ in 0..25 {
            session_manager.add_keystroke('a', true);
        }
        
        // Sleep for a short time to get a measurable duration
        tokio::time::sleep(Duration::from_millis(100)).await;
        
        let wpm = session_manager.get_wpm();
        assert!(wpm > 0.0);
    }
    
    #[tokio::test]
    async fn test_accuracy_calculation() {
        let config = Arc::new(Config::default());
        let mut session_manager = SessionManager::new(config);
        
        // 8 correct out of 10 keystrokes = 80% accuracy
        for _ in 0..8 {
            session_manager.add_keystroke('a', true);
        }
        for _ in 0..2 {
            session_manager.add_keystroke('b', false);
        }
        
        let accuracy = session_manager.get_accuracy();
        assert_eq!(accuracy, 80.0);
    }
}
