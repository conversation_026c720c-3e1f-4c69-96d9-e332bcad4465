import tkinter as tk
from tkinter import font
import win32gui
import win32con
import win32api

class StatisticsDisplay:
    def __init__(self, config, session_manager):
        self.config = config
        self.session_manager = session_manager
        self.root = None
        self.is_visible = False
        self.labels = {}
        
        # Screen dimensions
        self.screen_width = win32api.GetSystemMetrics(0)
        self.screen_height = win32api.GetSystemMetrics(1)
    
    def create_window(self):
        """Create the statistics display window"""
        self.root = tk.Toplevel()
        self.root.withdraw()  # Hide initially
        
        # Configure window properties
        self.root.overrideredirect(True)  # Remove window decorations
        self.root.attributes('-topmost', True)  # Always on top
        self.root.configure(bg='#2C2C2C')  # Dark background
        
        # Set window size and position (top-right corner)
        window_width = 300
        window_height = 200
        x_position = self.screen_width - window_width - 20
        y_position = 20
        
        self.root.geometry(f"{window_width}x{window_height}+{x_position}+{y_position}")
        
        # Make window semi-transparent
        self._make_transparent()
        
        # Create the statistics display
        self._create_statistics_layout()
        
        return self.root
    
    def _make_transparent(self):
        """Make the window semi-transparent"""
        try:
            hwnd = self.root.winfo_id()
            # Get current window style
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            # Add layered flag
            style |= win32con.WS_EX_LAYERED
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, style)
            # Set opacity (80% opaque)
            opacity = int(255 * 0.8)
            win32gui.SetLayeredWindowAttributes(hwnd, 0, opacity, win32con.LWA_ALPHA)
        except Exception as e:
            print(f"Warning: Could not make statistics window transparent: {e}")
    
    def _create_statistics_layout(self):
        """Create the layout for statistics display"""
        # Title
        title_font = font.Font(family=self.config.get('font_family'), size=16, weight='bold')
        title_label = tk.Label(
            self.root,
            text="Typing Statistics",
            font=title_font,
            fg='#FFFFFF',
            bg='#2C2C2C'
        )
        title_label.pack(pady=(10, 5))
        
        # Statistics font
        stats_font = font.Font(family=self.config.get('font_family'), size=12)
        
        # Create labels for each statistic
        stats_frame = tk.Frame(self.root, bg='#2C2C2C')
        stats_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Session status
        self.labels['status'] = tk.Label(
            stats_frame,
            text="Status: Inactive",
            font=stats_font,
            fg='#CCCCCC',
            bg='#2C2C2C',
            anchor='w'
        )
        self.labels['status'].pack(fill='x', pady=2)
        
        # Duration
        self.labels['duration'] = tk.Label(
            stats_frame,
            text="Duration: 0:00",
            font=stats_font,
            fg='#CCCCCC',
            bg='#2C2C2C',
            anchor='w'
        )
        self.labels['duration'].pack(fill='x', pady=2)
        
        # Words per minute
        self.labels['wpm'] = tk.Label(
            stats_frame,
            text="WPM: 0",
            font=stats_font,
            fg='#CCCCCC',
            bg='#2C2C2C',
            anchor='w'
        )
        self.labels['wpm'].pack(fill='x', pady=2)
        
        # Accuracy
        self.labels['accuracy'] = tk.Label(
            stats_frame,
            text="Accuracy: 0%",
            font=stats_font,
            fg='#CCCCCC',
            bg='#2C2C2C',
            anchor='w'
        )
        self.labels['accuracy'].pack(fill='x', pady=2)
        
        # Words completed
        self.labels['words'] = tk.Label(
            stats_frame,
            text="Words: 0",
            font=stats_font,
            fg='#CCCCCC',
            bg='#2C2C2C',
            anchor='w'
        )
        self.labels['words'].pack(fill='x', pady=2)
        
        # Keystrokes
        self.labels['keystrokes'] = tk.Label(
            stats_frame,
            text="Keystrokes: 0",
            font=stats_font,
            fg='#CCCCCC',
            bg='#2C2C2C',
            anchor='w'
        )
        self.labels['keystrokes'].pack(fill='x', pady=2)
        
        # Instructions
        instruction_font = font.Font(family=self.config.get('font_family'), size=10)
        instruction_label = tk.Label(
            self.root,
            text="Press hotkey again to hide",
            font=instruction_font,
            fg='#888888',
            bg='#2C2C2C'
        )
        instruction_label.pack(side='bottom', pady=5)
    
    def update_statistics(self):
        """Update the statistics display with current data"""
        if not self.root or not self.is_visible:
            return
        
        stats = self.session_manager.get_statistics()
        
        # Update status
        status_text = "Active" if stats['active'] else "Inactive"
        status_color = "#00FF00" if stats['active'] else "#FF6666"
        self.labels['status'].config(text=f"Status: {status_text}", fg=status_color)
        
        # Update duration
        duration = stats['duration']
        minutes = int(duration // 60)
        seconds = int(duration % 60)
        self.labels['duration'].config(text=f"Duration: {minutes}:{seconds:02d}")
        
        # Update WPM
        wpm = stats['wpm']
        self.labels['wpm'].config(text=f"WPM: {wpm:.1f}")
        
        # Update accuracy
        accuracy = stats['accuracy']
        accuracy_color = self._get_accuracy_color(accuracy)
        self.labels['accuracy'].config(text=f"Accuracy: {accuracy:.1f}%", fg=accuracy_color)
        
        # Update words completed
        self.labels['words'].config(text=f"Words: {stats['words_completed']}")
        
        # Update keystrokes
        correct = stats['correct_keystrokes']
        incorrect = stats['incorrect_keystrokes']
        total = stats['total_keystrokes']
        self.labels['keystrokes'].config(text=f"Keystrokes: {total} ({correct}✓/{incorrect}✗)")
    
    def _get_accuracy_color(self, accuracy):
        """Get color based on accuracy percentage"""
        if accuracy >= 95:
            return "#00FF00"  # Green for excellent
        elif accuracy >= 85:
            return "#FFFF00"  # Yellow for good
        elif accuracy >= 70:
            return "#FFA500"  # Orange for fair
        else:
            return "#FF6666"  # Red for poor
    
    def show(self):
        """Show the statistics window"""
        if self.root:
            self.root.deiconify()
            self.root.lift()
            self.is_visible = True
            self.update_statistics()
    
    def hide(self):
        """Hide the statistics window"""
        if self.root:
            self.root.withdraw()
            self.is_visible = False
    
    def toggle_visibility(self):
        """Toggle statistics window visibility"""
        if self.is_visible:
            self.hide()
        else:
            self.show()
    
    def update(self):
        """Update the window (call this regularly when visible)"""
        if self.root and self.is_visible:
            self.update_statistics()
            self.root.update_idletasks()
    
    def destroy(self):
        """Destroy the statistics window"""
        if self.root:
            self.root.destroy()
            self.root = None
            self.labels.clear()


class StatisticsTracker:
    def __init__(self):
        self.session_history = []
        self.total_sessions = 0
        self.total_time = 0
        self.total_words = 0
        self.total_keystrokes = 0
        self.best_wpm = 0
        self.best_accuracy = 0
    
    def add_session(self, session_stats):
        """Add a completed session to the history"""
        self.session_history.append(session_stats.copy())
        self.total_sessions += 1
        self.total_time += session_stats['duration']
        self.total_words += session_stats['words_completed']
        self.total_keystrokes += session_stats['total_keystrokes']
        
        # Update best scores
        if session_stats['wpm'] > self.best_wpm:
            self.best_wpm = session_stats['wpm']
        
        if session_stats['accuracy'] > self.best_accuracy:
            self.best_accuracy = session_stats['accuracy']
        
        # Keep only last 100 sessions to prevent memory bloat
        if len(self.session_history) > 100:
            self.session_history.pop(0)
    
    def get_overall_statistics(self):
        """Get overall statistics across all sessions"""
        if self.total_sessions == 0:
            return {
                'total_sessions': 0,
                'total_time': 0,
                'average_wpm': 0,
                'average_accuracy': 0,
                'best_wpm': 0,
                'best_accuracy': 0,
                'total_words': 0,
                'total_keystrokes': 0
            }
        
        # Calculate averages from recent sessions
        recent_sessions = self.session_history[-20:]  # Last 20 sessions
        if recent_sessions:
            avg_wpm = sum(s['wpm'] for s in recent_sessions) / len(recent_sessions)
            avg_accuracy = sum(s['accuracy'] for s in recent_sessions) / len(recent_sessions)
        else:
            avg_wpm = 0
            avg_accuracy = 0
        
        return {
            'total_sessions': self.total_sessions,
            'total_time': self.total_time,
            'average_wpm': avg_wpm,
            'average_accuracy': avg_accuracy,
            'best_wpm': self.best_wpm,
            'best_accuracy': self.best_accuracy,
            'total_words': self.total_words,
            'total_keystrokes': self.total_keystrokes
        }
    
    def get_recent_sessions(self, count=10):
        """Get the most recent sessions"""
        return self.session_history[-count:] if self.session_history else []
