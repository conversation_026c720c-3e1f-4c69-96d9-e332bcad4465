import tkinter as tk
from tkinter import font
import win32gui
import win32con
import win32api

class OverlayWindow:
    def __init__(self, config):
        self.config = config
        self.root = None
        self.canvas = None
        self.is_visible = False
        self.screen_width = win32api.GetSystemMetrics(0)
        self.screen_height = win32api.GetSystemMetrics(1)
        
    def create_window(self):
        """Create the overlay window"""
        self.root = tk.Tk()
        self.root.withdraw()  # Hide initially
        
        # Configure window properties
        self.root.overrideredirect(True)  # Remove window decorations
        self.root.attributes('-topmost', True)  # Always on top
        self.root.attributes('-transparentcolor', self.config.get('background_color'))
        self.root.configure(bg=self.config.get('background_color'))
        
        # Set window size and position
        window_width = self.screen_width
        window_height = 200  # Height for text display
        y_position = (self.screen_height - window_height) // 2
        
        self.root.geometry(f"{window_width}x{window_height}+0+{y_position}")
        
        # Create canvas for text rendering
        self.canvas = tk.Canvas(
            self.root,
            width=window_width,
            height=window_height,
            bg=self.config.get('background_color'),
            highlightthickness=0
        )
        self.canvas.pack()
        
        # Make window click-through
        self._make_click_through()
        
        return self.root
    
    def _make_click_through(self):
        """Make the window click-through so it doesn't interfere with other applications"""
        try:
            hwnd = self.root.winfo_id()
            # Get current window style
            style = win32gui.GetWindowLong(hwnd, win32con.GWL_EXSTYLE)
            # Add layered and transparent flags
            style |= win32con.WS_EX_LAYERED | win32con.WS_EX_TRANSPARENT
            win32gui.SetWindowLong(hwnd, win32con.GWL_EXSTYLE, style)
            # Set opacity
            opacity = int(255 * self.config.get('window_opacity'))
            win32gui.SetLayeredWindowAttributes(hwnd, 0, opacity, win32con.LWA_ALPHA)
        except Exception as e:
            print(f"Warning: Could not make window click-through: {e}")
    
    def show(self):
        """Show the overlay window"""
        if self.root:
            self.root.deiconify()
            self.root.lift()
            self.is_visible = True
    
    def hide(self):
        """Hide the overlay window"""
        if self.root:
            self.root.withdraw()
            self.is_visible = False
    
    def toggle_visibility(self):
        """Toggle window visibility"""
        if self.is_visible:
            self.hide()
        else:
            self.show()
    
    def clear_canvas(self):
        """Clear all text from the canvas"""
        if self.canvas:
            self.canvas.delete("all")
    
    def draw_text(self, text, x, y, color=None, font_size=None):
        """Draw text on the canvas"""
        if not self.canvas:
            return None
            
        color = color or self.config.get('text_color')
        font_size = font_size or self.config.get('font_size')
        font_family = self.config.get('font_family')
        
        text_font = font.Font(family=font_family, size=font_size, weight='normal')
        
        return self.canvas.create_text(
            x, y,
            text=text,
            fill=color,
            font=text_font,
            anchor='center'
        )
    
    def update_text_position(self, text_id, x, y):
        """Update the position of existing text"""
        if self.canvas and text_id:
            self.canvas.coords(text_id, x, y)
    
    def delete_text(self, text_id):
        """Delete specific text from canvas"""
        if self.canvas and text_id:
            self.canvas.delete(text_id)
    
    def get_center_x(self):
        """Get the center X coordinate of the screen"""
        return self.screen_width // 2
    
    def get_center_y(self):
        """Get the center Y coordinate of the text area"""
        return 100  # Middle of the 200px height window
    
    def update(self):
        """Update the window (call this regularly)"""
        if self.root:
            self.root.update_idletasks()
            self.root.update()
    
    def destroy(self):
        """Destroy the window"""
        if self.root:
            self.root.destroy()
            self.root = None
            self.canvas = None
