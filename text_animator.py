from tkinter import font


class TextAnimator:
    def __init__(self, overlay_window, config):
        self.overlay = overlay_window
        self.config = config
        self.words = []
        self.word_objects = []  # List of dictionaries containing word info
        self.current_word_index = 0
        self.typed_chars = ""
        self.animation_timer = None
        
    def set_words(self, word_list):
        """Set the list of words to display"""
        self.words = word_list
        self.current_word_index = 0
        self.typed_chars = ""
        self.clear_display()
        self.create_word_display()
    
    def clear_display(self):
        """Clear all displayed words"""
        if self.overlay.canvas:
            self.overlay.clear_canvas()
        self.word_objects.clear()
    
    def create_word_display(self):
        """Create the initial word display"""
        if not self.words:
            return
            
        center_x = self.overlay.get_center_x()
        center_y = self.overlay.get_center_y()
        
        # Calculate font metrics
        font_family = self.config.get('font_family')
        font_size = self.config.get('font_size')
        test_font = font.Font(family=font_family, size=font_size)
        
        # Calculate spacing between words
        space_width = test_font.measure(" ")
        word_spacing = space_width * 2
        
        # Calculate total width of all words
        total_width = 0
        word_widths = []
        for word in self.words:
            width = test_font.measure(word)
            word_widths.append(width)
            total_width += width
        total_width += word_spacing * (len(self.words) - 1)
        
        # Position words starting from center
        current_x = center_x
        
        for i, word in enumerate(self.words):
            # Determine color based on word status
            if i < self.current_word_index:
                color = "#888888"  # Completed words (gray)
            elif i == self.current_word_index:
                color = self.config.get('text_color')  # Current word (white)
            else:
                color = "#CCCCCC"  # Future words (light gray)
            
            # Create text object
            text_id = self.overlay.draw_text(word, current_x, center_y, color)
            
            word_obj = {
                'text': word,
                'text_id': text_id,
                'x': current_x,
                'y': center_y,
                'width': word_widths[i],
                'index': i,
                'completed': i < self.current_word_index
            }
            self.word_objects.append(word_obj)
            
            # Move to next word position
            current_x += word_widths[i] + word_spacing
    
    def update_current_word_display(self):
        """Update the display of the current word being typed"""
        if (self.current_word_index >= len(self.word_objects) or
            self.current_word_index >= len(self.words)):
            return

        current_word = self.words[self.current_word_index]
        word_obj = self.word_objects[self.current_word_index]

        # Check if typed part is correct
        is_correct = current_word.startswith(self.typed_chars)

        # Determine color based on correctness
        if len(self.typed_chars) == 0:
            color = self.config.get('text_color')  # Default color
        elif is_correct:
            color = "#00FF00"  # Green for correct
        else:
            color = "#FF0000"  # Red for incorrect

        # Update the word display with current color
        if word_obj['text_id']:
            self.overlay.delete_text(word_obj['text_id'])

        # Create the display text with visual feedback
        display_text = current_word
        if len(self.typed_chars) > 0:
            # Show progress with underline or different styling
            typed_part = self.typed_chars
            remaining_part = current_word[len(self.typed_chars):]
            if is_correct:
                display_text = f"{typed_part}|{remaining_part}"
            else:
                display_text = f"[{typed_part}]{remaining_part}"

        word_obj['text_id'] = self.overlay.draw_text(
            display_text,
            word_obj['x'],
            word_obj['y'],
            color
        )
    
    def add_character(self, char):
        """Add a typed character"""
        if self.current_word_index >= len(self.words):
            return False
            
        current_word = self.words[self.current_word_index]
        
        if char == ' ':
            # Space pressed - check if current word is complete
            if self.typed_chars == current_word:
                self.complete_current_word()
                return True
            else:
                # Word not complete, ignore space or handle as error
                return False
        else:
            # Regular character
            self.typed_chars += char
            self.update_current_word_display()
            
            # Check if word is complete
            if self.typed_chars == current_word:
                # Word completed, but wait for space to move to next
                pass
            elif len(self.typed_chars) > len(current_word):
                # Too many characters, handle error
                pass
                
            return True
    
    def remove_character(self):
        """Remove the last typed character (backspace)"""
        if self.typed_chars:
            self.typed_chars = self.typed_chars[:-1]
            self.update_current_word_display()
            return True
        return False
    
    def complete_current_word(self):
        """Complete the current word and move to next"""
        if self.current_word_index < len(self.word_objects):
            # Mark current word as completed
            word_obj = self.word_objects[self.current_word_index]
            word_obj['completed'] = True
            
            # Update display color to completed
            if word_obj['text_id']:
                self.overlay.delete_text(word_obj['text_id'])
                word_obj['text_id'] = self.overlay.draw_text(
                    word_obj['text'],
                    word_obj['x'],
                    word_obj['y'],
                    "#888888"  # Gray for completed
                )
        
        # Move to next word
        self.current_word_index += 1
        self.typed_chars = ""
        
        # Animate words moving left
        self.animate_words_left()
        
        # Check if all words are completed
        if self.current_word_index >= len(self.words):
            return True  # All words completed
        
        return False
    
    def animate_words_left(self):
        """Animate all words moving to the left"""
        if not self.word_objects:
            return
            
        # Calculate how much to move (width of completed word + spacing)
        if self.current_word_index > 0:
            completed_word = self.word_objects[self.current_word_index - 1]
            move_distance = completed_word['width'] + 20  # 20 for spacing
            
            # Move all words left
            for word_obj in self.word_objects:
                word_obj['x'] -= move_distance
                if word_obj['text_id']:
                    self.overlay.update_text_position(
                        word_obj['text_id'],
                        word_obj['x'],
                        word_obj['y']
                    )
    
    def get_current_word(self):
        """Get the current word being typed"""
        if self.current_word_index < len(self.words):
            return self.words[self.current_word_index]
        return None
    
    def get_typed_text(self):
        """Get the currently typed text"""
        return self.typed_chars
    
    def is_complete(self):
        """Check if all words are completed"""
        return self.current_word_index >= len(self.words)
    
    def reset(self):
        """Reset the animator"""
        self.current_word_index = 0
        self.typed_chars = ""
        self.clear_display()
        if self.words:
            self.create_word_display()
