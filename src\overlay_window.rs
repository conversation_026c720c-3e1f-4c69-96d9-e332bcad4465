use std::sync::Arc;
use std::ffi::OsStr;
use std::os::windows::ffi::OsStrExt;
use std::ptr;
use std::mem;

use winapi::um::winuser::*;
use winapi::um::wingdi::*;
use winapi::um::libloaderapi::GetModuleHandleW;
use winapi::shared::windef::*;
use winapi::shared::minwindef::*;
use winapi::um::errhandlingapi::GetLastError;

use crate::config::Config;

pub struct OverlayWindow {
    config: Arc<Config>,
    hwnd: Option<HWND>,
    hdc: Option<HDC>,
    is_visible: bool,
    screen_width: i32,
    screen_height: i32,
    window_width: i32,
    window_height: i32,
}

impl OverlayWindow {
    pub fn new(config: Arc<Config>) -> Result<Self, Box<dyn std::error::Error>> {
        let screen_width = unsafe { GetSystemMetrics(SM_CXSCREEN) };
        let screen_height = unsafe { GetSystemMetrics(SM_CYSCREEN) };
        
        Ok(OverlayWindow {
            config,
            hwnd: None,
            hdc: None,
            is_visible: false,
            screen_width,
            screen_height,
            window_width: screen_width,
            window_height: 200, // Fixed height for text display
        })
    }
    
    pub fn create_window(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            // Register window class
            let class_name = to_wide_string("TypewriterOverlayClass");
            let window_title = to_wide_string("Typewriter Overlay");
            
            let wc = WNDCLASSEXW {
                cbSize: mem::size_of::<WNDCLASSEXW>() as u32,
                style: CS_HREDRAW | CS_VREDRAW,
                lpfnWndProc: Some(window_proc),
                cbClsExtra: 0,
                cbWndExtra: 0,
                hInstance: GetModuleHandleW(ptr::null()),
                hIcon: ptr::null_mut(),
                hCursor: LoadCursorW(ptr::null_mut(), IDC_ARROW),
                hbrBackground: ptr::null_mut(), // Transparent background
                lpszMenuName: ptr::null(),
                lpszClassName: class_name.as_ptr(),
                hIconSm: ptr::null_mut(),
            };
            
            if RegisterClassExW(&wc) == 0 {
                let error = GetLastError();
                if error != 1410 { // ERROR_CLASS_ALREADY_EXISTS
                    return Err(format!("Failed to register window class: {}", error).into());
                }
            }
            
            // Calculate window position (centered vertically)
            let y_position = (self.screen_height - self.window_height) / 2;
            
            // Create window
            let hwnd = CreateWindowExW(
                WS_EX_LAYERED | WS_EX_TRANSPARENT | WS_EX_TOPMOST | WS_EX_TOOLWINDOW,
                class_name.as_ptr(),
                window_title.as_ptr(),
                WS_POPUP,
                0,
                y_position,
                self.window_width,
                self.window_height,
                ptr::null_mut(),
                ptr::null_mut(),
                GetModuleHandleW(ptr::null()),
                ptr::null_mut(),
            );
            
            if hwnd.is_null() {
                return Err(format!("Failed to create window: {}", GetLastError()).into());
            }
            
            self.hwnd = Some(hwnd);
            
            // Set window transparency
            let opacity = (255.0 * self.config.window_opacity) as u8;
            SetLayeredWindowAttributes(hwnd, 0, opacity, LWA_ALPHA);
            
            // Get device context
            let hdc = GetDC(hwnd);
            if hdc.is_null() {
                return Err("Failed to get device context".into());
            }
            self.hdc = Some(hdc);
            
            // Set up drawing context
            SetBkMode(hdc, TRANSPARENT as i32);
            
            Ok(())
        }
    }
    
    pub fn show(&mut self) {
        if let Some(hwnd) = self.hwnd {
            unsafe {
                ShowWindow(hwnd, SW_SHOW);
                UpdateWindow(hwnd);
                self.is_visible = true;
            }
        }
    }
    
    pub fn hide(&mut self) {
        if let Some(hwnd) = self.hwnd {
            unsafe {
                ShowWindow(hwnd, SW_HIDE);
                self.is_visible = false;
            }
        }
    }
    
    pub fn toggle_visibility(&mut self) {
        if self.is_visible {
            self.hide();
        } else {
            self.show();
        }
    }
    
    pub fn is_visible(&self) -> bool {
        self.is_visible
    }
    
    pub fn clear_canvas(&self) {
        if let (Some(hwnd), Some(hdc)) = (self.hwnd, self.hdc) {
            unsafe {
                let mut rect = RECT {
                    left: 0,
                    top: 0,
                    right: self.window_width,
                    bottom: self.window_height,
                };
                
                // Clear with transparent background
                let bg_color = parse_color(&self.config.background_color);
                let brush = CreateSolidBrush(bg_color);
                FillRect(hdc, &rect, brush);
                DeleteObject(brush as *mut _);
                
                InvalidateRect(hwnd, ptr::null(), TRUE);
            }
        }
    }
    
    pub fn draw_text(&self, text: &str, x: i32, y: i32, color: Option<&str>) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(hdc) = self.hdc {
            unsafe {
                let text_color = color.unwrap_or(&self.config.text_color);
                let color_value = parse_color(text_color);
                
                SetTextColor(hdc, color_value);
                
                // Create font
                let font_size = -(self.config.font_size as i32 * GetDeviceCaps(hdc, LOGPIXELSY) / 72);
                let font_name = to_wide_string(&self.config.font_family);
                
                let font = CreateFontW(
                    font_size,
                    0,
                    0,
                    0,
                    FW_NORMAL,
                    0,
                    0,
                    0,
                    DEFAULT_CHARSET,
                    OUT_DEFAULT_PRECIS,
                    CLIP_DEFAULT_PRECIS,
                    DEFAULT_QUALITY,
                    DEFAULT_PITCH | FF_DONTCARE,
                    font_name.as_ptr(),
                );
                
                let old_font = SelectObject(hdc, font as *mut _);
                
                // Draw text
                let wide_text = to_wide_string(text);
                TextOutW(hdc, x, y, wide_text.as_ptr(), wide_text.len() as i32 - 1);
                
                // Cleanup
                SelectObject(hdc, old_font);
                DeleteObject(font as *mut _);
            }
        }
        
        Ok(())
    }
    
    pub async fn update(&self) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(hwnd) = self.hwnd {
            unsafe {
                let mut msg = MSG {
                    hwnd: ptr::null_mut(),
                    message: 0,
                    wParam: 0,
                    lParam: 0,
                    time: 0,
                    pt: POINT { x: 0, y: 0 },
                };
                
                // Process pending messages
                while PeekMessageW(&mut msg, hwnd, 0, 0, PM_REMOVE) != 0 {
                    TranslateMessage(&msg);
                    DispatchMessageW(&msg);
                }
            }
        }
        
        Ok(())
    }
    
    pub fn get_window_dimensions(&self) -> (i32, i32) {
        (self.window_width, self.window_height)
    }
    
    pub fn get_center_position(&self) -> (i32, i32) {
        (self.window_width / 2, self.window_height / 2)
    }
}

impl Drop for OverlayWindow {
    fn drop(&mut self) {
        unsafe {
            if let Some(hdc) = self.hdc {
                if let Some(hwnd) = self.hwnd {
                    ReleaseDC(hwnd, hdc);
                }
            }
            
            if let Some(hwnd) = self.hwnd {
                DestroyWindow(hwnd);
            }
        }
    }
}

// Helper functions
fn to_wide_string(s: &str) -> Vec<u16> {
    OsStr::new(s).encode_wide().chain(Some(0)).collect()
}

fn parse_color(color_str: &str) -> COLORREF {
    if color_str.starts_with('#') && color_str.len() == 7 {
        if let Ok(value) = u32::from_str_radix(&color_str[1..], 16) {
            // Convert from RGB to BGR (Windows format)
            let r = (value >> 16) & 0xFF;
            let g = (value >> 8) & 0xFF;
            let b = value & 0xFF;
            return (b << 16) | (g << 8) | r;
        }
    }
    0x00FFFFFF // Default to white
}

unsafe extern "system" fn window_proc(
    hwnd: HWND,
    msg: UINT,
    wparam: WPARAM,
    lparam: LPARAM,
) -> LRESULT {
    match msg {
        WM_PAINT => {
            let mut ps = PAINTSTRUCT {
                hdc: ptr::null_mut(),
                fErase: 0,
                rcPaint: RECT { left: 0, top: 0, right: 0, bottom: 0 },
                fRestore: 0,
                fIncUpdate: 0,
                rgbReserved: [0; 32],
            };
            
            let hdc = BeginPaint(hwnd, &mut ps);
            // Custom painting would go here
            EndPaint(hwnd, &ps);
            0
        }
        WM_DESTROY => {
            PostQuitMessage(0);
            0
        }
        _ => DefWindowProcW(hwnd, msg, wparam, lparam),
    }
}
