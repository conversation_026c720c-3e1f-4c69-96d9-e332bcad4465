use std::sync::Arc;
use std::ptr;
use tokio::sync::Mutex;
use winapi::um::winuser::*;
use winapi::shared::minwindef::*;
use winapi::shared::windef::*;

use crate::text_animator::TextAnimator;
use crate::session_manager::SessionManager;

pub struct InputHandler {
    text_animator: Arc<Mutex<TextAnimator>>,
    session_manager: Arc<Mutex<SessionManager>>,
    is_active: bool,
    hook_handle: Option<HHOOK>,
    ignore_keys: Vec<i32>,
}

impl InputHandler {
    pub fn new(
        text_animator: Arc<Mutex<TextAnimator>>,
        session_manager: Arc<Mutex<SessionManager>>,
    ) -> Self {
        InputHandler {
            text_animator,
            session_manager,
            is_active: false,
            hook_handle: None,
            ignore_keys: vec![
                VK_CONTROL as i32,
                VK_SHIFT as i32,
                VK_MENU as i32, // Alt key
                VK_LWIN as i32,
                VK_RWIN as i32,
                VK_CAPITAL as i32, // Caps Lock
                VK_NUMLOCK as i32,
                VK_SCROLL as i32,
                VK_TAB as i32,
                VK_ESCAPE as i32,
                VK_F1 as i32,
                VK_F2 as i32,
                VK_F3 as i32,
                VK_F4 as i32,
                VK_F5 as i32,
                VK_F6 as i32,
                VK_F7 as i32,
                VK_F8 as i32,
                VK_F9 as i32,
                VK_F10 as i32,
                VK_F11 as i32,
                VK_F12 as i32,
            ],
        }
    }
    
    pub async fn start_listening(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if self.hook_handle.is_some() {
            return Ok(()); // Already listening
        }
        
        unsafe {
            // Install low-level keyboard hook
            let hook = SetWindowsHookExW(
                WH_KEYBOARD_LL,
                Some(keyboard_hook_proc),
                GetModuleHandleW(ptr::null()),
                0,
            );
            
            if hook.is_null() {
                return Err("Failed to install keyboard hook".into());
            }
            
            self.hook_handle = Some(hook);
        }
        
        Ok(())
    }
    
    pub async fn stop_listening(&mut self) {
        if let Some(hook) = self.hook_handle.take() {
            unsafe {
                UnhookWindowsHookExW(hook);
            }
        }
    }
    
    pub fn activate(&mut self) {
        self.is_active = true;
    }
    
    pub fn deactivate(&mut self) {
        self.is_active = false;
    }
    
    pub fn is_active(&self) -> bool {
        self.is_active
    }
    
    async fn handle_character(&self, ch: char) {
        if !self.is_active {
            return;
        }
        
        // Update session activity
        {
            let mut session_manager = self.session_manager.lock().await;
            session_manager.update_activity();
        }
        
        // Add character to text animator
        let success = {
            let mut text_animator = self.text_animator.lock().await;
            text_animator.add_character(ch).await
        };
        
        // Track statistics
        if success {
            let current_word = {
                let text_animator = self.text_animator.lock().await;
                text_animator.get_current_word()
            };
            
            let typed_text = {
                let text_animator = self.text_animator.lock().await;
                text_animator.get_typed_text()
            };
            
            if let Some(current_word) = current_word {
                if !typed_text.is_empty() {
                    // Check if character is correct
                    let expected_char = if typed_text.len() <= current_word.len() {
                        current_word.chars().nth(typed_text.len() - 1)
                    } else {
                        None
                    };
                    
                    let is_correct = expected_char == Some(ch);
                    
                    // Update session statistics
                    let mut session_manager = self.session_manager.lock().await;
                    session_manager.add_keystroke(ch, is_correct);
                }
            }
        }
        
        // Check if all words are completed
        let is_complete = {
            let text_animator = self.text_animator.lock().await;
            text_animator.is_complete()
        };
        
        if is_complete {
            self.handle_completion().await;
        }
    }
    
    async fn handle_backspace(&self) {
        if !self.is_active {
            return;
        }
        
        // Update session activity
        {
            let mut session_manager = self.session_manager.lock().await;
            session_manager.update_activity();
            session_manager.add_backspace();
        }
        
        // Remove character from text animator
        let mut text_animator = self.text_animator.lock().await;
        text_animator.remove_character().await;
    }
    
    async fn handle_completion(&self) {
        // Mark word as completed in session manager
        let mut session_manager = self.session_manager.lock().await;
        session_manager.add_word_completed();
        
        // Could trigger new word generation here
        // For now, just log completion
        println!("Word sequence completed!");
    }
}

impl Drop for InputHandler {
    fn drop(&mut self) {
        if let Some(hook) = self.hook_handle.take() {
            unsafe {
                UnhookWindowsHookExW(hook);
            }
        }
    }
}

// Global keyboard hook procedure
unsafe extern "system" fn keyboard_hook_proc(
    n_code: i32,
    w_param: WPARAM,
    l_param: LPARAM,
) -> LRESULT {
    if n_code >= 0 {
        if w_param == WM_KEYDOWN as usize || w_param == WM_SYSKEYDOWN as usize {
            let kb_struct = *(l_param as *const KBDLLHOOKSTRUCT);
            let vk_code = kb_struct.vkCode as i32;
            
            // Convert virtual key code to character
            if let Some(ch) = vk_to_char(vk_code) {
                // Here we would need to access the InputHandler instance
                // In a real implementation, we'd use a global static or other mechanism
                // to communicate with the InputHandler
                
                // For now, we'll just pass through all keys
                // The actual input handling would be done in the main application loop
            }
        }
    }
    
    // Call next hook
    CallNextHookEx(ptr::null_mut(), n_code, w_param, l_param)
}

fn vk_to_char(vk_code: i32) -> Option<char> {
    match vk_code {
        VK_SPACE => Some(' '),
        VK_BACK => Some('\x08'), // Backspace
        VK_RETURN => Some('\r'),
        VK_TAB => Some('\t'),
        // Letters
        0x41..=0x5A => {
            // A-Z
            let ch = (vk_code - 0x41 + 0x61) as u8; // Convert to lowercase
            Some(ch as char)
        }
        // Numbers
        0x30..=0x39 => {
            // 0-9
            Some((vk_code as u8) as char)
        }
        // Punctuation and symbols would need more complex handling
        // including checking shift state, etc.
        _ => None,
    }
}

// Simplified input handler for demonstration
pub struct SimpleInputHandler {
    text_animator: Arc<Mutex<TextAnimator>>,
    session_manager: Arc<Mutex<SessionManager>>,
    is_active: bool,
}

impl SimpleInputHandler {
    pub fn new(
        text_animator: Arc<Mutex<TextAnimator>>,
        session_manager: Arc<Mutex<SessionManager>>,
    ) -> Self {
        SimpleInputHandler {
            text_animator,
            session_manager,
            is_active: false,
        }
    }
    
    pub async fn start_listening(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        self.is_active = true;
        println!("Input handler started (simplified mode)");
        Ok(())
    }
    
    pub async fn stop_listening(&mut self) {
        self.is_active = false;
        println!("Input handler stopped");
    }
    
    pub fn activate(&mut self) {
        self.is_active = true;
    }
    
    pub fn deactivate(&mut self) {
        self.is_active = false;
    }
    
    pub fn is_active(&self) -> bool {
        self.is_active
    }
    
    // Method to manually process input (for testing or simplified usage)
    pub async fn process_input(&self, input: &str) {
        if !self.is_active {
            return;
        }
        
        for ch in input.chars() {
            match ch {
                '\x08' => self.handle_backspace().await,
                _ => self.handle_character(ch).await,
            }
        }
    }
    
    async fn handle_character(&self, ch: char) {
        // Update session activity
        {
            let mut session_manager = self.session_manager.lock().await;
            session_manager.update_activity();
        }
        
        // Add character to text animator
        let success = {
            let mut text_animator = self.text_animator.lock().await;
            text_animator.add_character(ch).await
        };
        
        if success {
            // Track statistics
            let current_word = {
                let text_animator = self.text_animator.lock().await;
                text_animator.get_current_word()
            };
            
            let typed_text = {
                let text_animator = self.text_animator.lock().await;
                text_animator.get_typed_text()
            };
            
            if let Some(current_word) = current_word {
                if !typed_text.is_empty() {
                    let expected_char = if typed_text.len() <= current_word.len() {
                        current_word.chars().nth(typed_text.len() - 1)
                    } else {
                        None
                    };
                    
                    let is_correct = expected_char == Some(ch);
                    
                    let mut session_manager = self.session_manager.lock().await;
                    session_manager.add_keystroke(ch, is_correct);
                }
            }
        }
    }
    
    async fn handle_backspace(&self) {
        // Update session activity
        {
            let mut session_manager = self.session_manager.lock().await;
            session_manager.update_activity();
            session_manager.add_backspace();
        }
        
        // Remove character from text animator
        let mut text_animator = self.text_animator.lock().await;
        text_animator.remove_character().await;
    }
}
