use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Config {
    pub idle_timeout: f64,
    pub toggle_hotkey: String,
    pub stats_hotkey: String,
    pub font_size: u32,
    pub font_family: String,
    pub text_color: String,
    pub background_color: String,
    pub window_opacity: f64,
    pub animation_speed: u64,
    pub words_per_line: usize,
}

impl Default for Config {
    fn default() -> Self {
        Config {
            idle_timeout: 3.0,
            toggle_hotkey: "ctrl+shift+t".to_string(),
            stats_hotkey: "ctrl+shift+s".to_string(),
            font_size: 24,
            font_family: "Consolas".to_string(),
            text_color: "#FFFFFF".to_string(),
            background_color: "#000000".to_string(),
            window_opacity: 0.8,
            animation_speed: 50,
            words_per_line: 10,
        }
    }
}

impl Config {
    const CONFIG_FILE: &'static str = "config.json";
    
    pub fn load() -> Result<Self, Box<dyn std::error::Error>> {
        if Path::new(Self::CONFIG_FILE).exists() {
            let content = fs::read_to_string(Self::CONFIG_FILE)?;
            let mut config: Config = serde_json::from_str(&content)?;
            
            // Validate and fix any invalid values
            config.validate_and_fix();
            
            Ok(config)
        } else {
            // Create default config file
            let default_config = Config::default();
            default_config.save()?;
            Ok(default_config)
        }
    }
    
    pub fn save(&self) -> Result<(), Box<dyn std::error::Error>> {
        let content = serde_json::to_string_pretty(self)?;
        fs::write(Self::CONFIG_FILE, content)?;
        Ok(())
    }
    
    pub fn get<T>(&self, key: &str) -> Option<T>
    where
        T: Clone + From<f64> + From<String> + From<u32> + From<u64> + From<usize>,
    {
        match key {
            "idle_timeout" => Some(T::from(self.idle_timeout)),
            "toggle_hotkey" => Some(T::from(self.toggle_hotkey.clone())),
            "stats_hotkey" => Some(T::from(self.stats_hotkey.clone())),
            "font_size" => Some(T::from(self.font_size)),
            "font_family" => Some(T::from(self.font_family.clone())),
            "text_color" => Some(T::from(self.text_color.clone())),
            "background_color" => Some(T::from(self.background_color.clone())),
            "window_opacity" => Some(T::from(self.window_opacity)),
            "animation_speed" => Some(T::from(self.animation_speed)),
            "words_per_line" => Some(T::from(self.words_per_line)),
            _ => None,
        }
    }
    
    fn validate_and_fix(&mut self) {
        // Ensure values are within reasonable ranges
        if self.idle_timeout < 0.1 {
            self.idle_timeout = 0.1;
        } else if self.idle_timeout > 300.0 {
            self.idle_timeout = 300.0;
        }
        
        if self.font_size < 8 {
            self.font_size = 8;
        } else if self.font_size > 72 {
            self.font_size = 72;
        }
        
        if self.window_opacity < 0.1 {
            self.window_opacity = 0.1;
        } else if self.window_opacity > 1.0 {
            self.window_opacity = 1.0;
        }
        
        if self.animation_speed < 10 {
            self.animation_speed = 10;
        } else if self.animation_speed > 1000 {
            self.animation_speed = 1000;
        }
        
        if self.words_per_line < 1 {
            self.words_per_line = 1;
        } else if self.words_per_line > 50 {
            self.words_per_line = 50;
        }
        
        // Validate color format (basic check)
        if !self.text_color.starts_with('#') || self.text_color.len() != 7 {
            self.text_color = "#FFFFFF".to_string();
        }
        
        if !self.background_color.starts_with('#') || self.background_color.len() != 7 {
            self.background_color = "#000000".to_string();
        }
        
        // Ensure font family is not empty
        if self.font_family.trim().is_empty() {
            self.font_family = "Consolas".to_string();
        }
        
        // Validate hotkey format (basic check)
        if self.toggle_hotkey.trim().is_empty() {
            self.toggle_hotkey = "ctrl+shift+t".to_string();
        }
        
        if self.stats_hotkey.trim().is_empty() {
            self.stats_hotkey = "ctrl+shift+s".to_string();
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    
    #[test]
    fn test_default_config() {
        let config = Config::default();
        assert_eq!(config.idle_timeout, 3.0);
        assert_eq!(config.toggle_hotkey, "ctrl+shift+t");
        assert_eq!(config.font_size, 24);
    }
    
    #[test]
    fn test_config_validation() {
        let mut config = Config {
            idle_timeout: -1.0,
            font_size: 200,
            window_opacity: 2.0,
            animation_speed: 5,
            words_per_line: 0,
            text_color: "invalid".to_string(),
            ..Default::default()
        };
        
        config.validate_and_fix();
        
        assert_eq!(config.idle_timeout, 0.1);
        assert_eq!(config.font_size, 72);
        assert_eq!(config.window_opacity, 1.0);
        assert_eq!(config.animation_speed, 10);
        assert_eq!(config.words_per_line, 1);
        assert_eq!(config.text_color, "#FFFFFF");
    }
    
    #[test]
    fn test_config_serialization() {
        let config = Config::default();
        let json = serde_json::to_string(&config).unwrap();
        let deserialized: Config = serde_json::from_str(&json).unwrap();
        
        assert_eq!(config.idle_timeout, deserialized.idle_timeout);
        assert_eq!(config.toggle_hotkey, deserialized.toggle_hotkey);
    }
}
