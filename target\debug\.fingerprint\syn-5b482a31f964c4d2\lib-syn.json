{"rustc": 3926191382657067107, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 2225463790103693989, "path": 10129529437665725832, "deps": [[1988483478007900009, "unicode_ident", false, 8915027332810634021], [3060637413840920116, "proc_macro2", false, 10881252360266983418], [17990358020177143287, "quote", false, 6519500971883872266]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\syn-5b482a31f964c4d2\\dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}