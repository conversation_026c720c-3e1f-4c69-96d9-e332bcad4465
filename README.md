# Typewriter Overlay

A lightweight typing practice overlay for Windows that works above fullscreen applications.

## Features

- **Overlay Display**: Works above all applications, including fullscreen games and videos
- **Smooth Animation**: Words start in the center and move left as you type them
- **Global Hotkeys**: Toggle overlay and statistics without losing focus from other applications
- **Session Management**: Automatic session tracking with configurable idle timeout
- **Real-time Statistics**: WPM, accuracy, and session duration tracking
- **Resource Efficient**: Minimal CPU and memory usage
- **Customizable**: Configurable hotkeys, colors, fonts, and timing

## Requirements

- Windows 10 or later
- Python 3.7 or later

## Installation

1. Clone or download this repository
2. Install required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. Run the application:
   ```bash
   python main.py
   ```

2. Use the default hotkeys:
   - **Ctrl+Shift+T**: Toggle the typing overlay
   - **Ctrl+Shift+S**: Show/hide statistics

3. When the overlay is visible:
   - Type the words as they appear
   - Words will move left as you complete them
   - Press Space after each word to move to the next
   - Use Backspace to correct mistakes

4. Sessions automatically start when you begin typing and end after 3 seconds of inactivity (configurable)

## Configuration

The application creates a `config.json` file with customizable settings:

```json
{
  "idle_timeout": 3.0,
  "toggle_hotkey": "ctrl+shift+t",
  "stats_hotkey": "ctrl+shift+s",
  "font_size": 24,
  "font_family": "Consolas",
  "text_color": "#FFFFFF",
  "background_color": "#000000",
  "window_opacity": 0.8,
  "animation_speed": 50,
  "words_per_line": 10
}
```

### Configuration Options

- `idle_timeout`: Seconds of inactivity before session ends (default: 3.0)
- `toggle_hotkey`: Hotkey to show/hide overlay (default: "ctrl+shift+t")
- `stats_hotkey`: Hotkey to show/hide statistics (default: "ctrl+shift+s")
- `font_size`: Text font size (default: 24)
- `font_family`: Font family name (default: "Consolas")
- `text_color`: Text color in hex format (default: "#FFFFFF")
- `background_color`: Background color in hex format (default: "#000000")
- `window_opacity`: Window transparency (0.0 to 1.0, default: 0.8)
- `animation_speed`: Animation update interval in milliseconds (default: 50)
- `words_per_line`: Number of words to display per session (default: 10)

## How It Works

1. **Overlay Window**: Uses tkinter with Windows-specific APIs to create a transparent, always-on-top window that doesn't interfere with other applications
2. **Input Capture**: Monitors keyboard input globally using pynput
3. **Text Animation**: Smoothly animates text position as words are completed
4. **Session Tracking**: Automatically manages typing sessions with configurable timeout
5. **Statistics**: Real-time calculation of WPM, accuracy, and other metrics

## Troubleshooting

### Overlay not appearing above fullscreen applications
- Make sure you're running the application as administrator
- Some fullscreen applications may override the always-on-top behavior

### Hotkeys not working
- Check if other applications are using the same hotkey combinations
- Modify the hotkeys in the config.json file
- Restart the application after changing configuration

### High CPU usage
- Increase the `animation_speed` value in config.json to reduce update frequency
- Close the statistics window when not needed

### Permission errors
- Run the application as administrator if you encounter permission issues
- Some antivirus software may block global keyboard monitoring

## Performance

The application is designed to be resource-efficient:
- Minimal CPU usage when idle
- Low memory footprint
- Efficient rendering using tkinter's built-in optimization
- Background threads for non-blocking operations

## Development

The codebase is modular and well-structured:

- `main.py`: Main application entry point
- `config.py`: Configuration management
- `overlay_window.py`: Overlay window implementation
- `text_animator.py`: Text display and animation
- `input_handler.py`: Keyboard input processing and session management
- `statistics_display.py`: Statistics tracking and display
- `hotkey_manager.py`: Global hotkey handling
- `word_list.py`: Word list management

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.
