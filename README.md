# Typewriter Overlay (Rust Edition)

A lightweight typing practice overlay for Windows that works above fullscreen applications. This is a high-performance Rust implementation of the original Python version.

## Features

- **Overlay Display**: Works above all applications, including fullscreen games and videos
- **Smooth Animation**: Words start in the center and move left as you type them
- **Global Hotkeys**: Toggle overlay and statistics without losing focus from other applications
- **Session Management**: Automatic session tracking with configurable idle timeout
- **Real-time Statistics**: WPM, accuracy, and session duration tracking
- **Resource Efficient**: Minimal CPU and memory usage with Rust's performance
- **Customizable**: Configurable hotkeys, colors, fonts, and timing
- **Native Windows Integration**: Direct Windows API usage for optimal performance

## Requirements

- Windows 10 or later
- Rust 1.70 or later (for building from source)

## Installation

### Option 1: Build from Source
1. Install Rust from [rustup.rs](https://rustup.rs/)
2. Clone or download this repository
3. Build the application:
   ```bash
   cargo build --release
   ```

### Option 2: Quick Start
1. Clone or download this repository
2. Run the build script:
   ```bash
   run.bat
   ```

## Usage

### Running the Application
1. **Release mode** (recommended):
   ```bash
   run.bat
   ```
   or
   ```bash
   cargo run --release
   ```

2. **Development mode**:
   ```bash
   run-dev.bat
   ```
   or
   ```bash
   cargo run
   ```

2. Use the default hotkeys:
   - **Ctrl+Shift+T**: Toggle the typing overlay
   - **Ctrl+Shift+S**: Show/hide statistics

3. When the overlay is visible:
   - Type the words as they appear
   - Words will move left as you complete them
   - Press Space after each word to move to the next
   - Use Backspace to correct mistakes

4. Sessions automatically start when you begin typing and end after 3 seconds of inactivity (configurable)

## Configuration

The application creates a `config.json` file with customizable settings:

```json
{
  "idle_timeout": 3.0,
  "toggle_hotkey": "ctrl+shift+t",
  "stats_hotkey": "ctrl+shift+s",
  "font_size": 24,
  "font_family": "Consolas",
  "text_color": "#FFFFFF",
  "background_color": "#000000",
  "window_opacity": 0.8,
  "animation_speed": 50,
  "words_per_line": 10
}
```

### Configuration Options

- `idle_timeout`: Seconds of inactivity before session ends (default: 3.0)
- `toggle_hotkey`: Hotkey to show/hide overlay (default: "ctrl+shift+t")
- `stats_hotkey`: Hotkey to show/hide statistics (default: "ctrl+shift+s")
- `font_size`: Text font size (default: 24)
- `font_family`: Font family name (default: "Consolas")
- `text_color`: Text color in hex format (default: "#FFFFFF")
- `background_color`: Background color in hex format (default: "#000000")
- `window_opacity`: Window transparency (0.0 to 1.0, default: 0.8)
- `animation_speed`: Animation update interval in milliseconds (default: 50)
- `words_per_line`: Number of words to display per session (default: 10)

## How It Works

1. **Overlay Window**: Uses Windows API directly to create a transparent, always-on-top window that doesn't interfere with other applications
2. **Input Capture**: Monitors keyboard input globally using Windows low-level keyboard hooks
3. **Text Animation**: Smoothly animates text position as words are completed using native Windows GDI
4. **Session Tracking**: Automatically manages typing sessions with configurable timeout using async Rust
5. **Statistics**: Real-time calculation of WPM, accuracy, and other metrics with high precision

## Troubleshooting

### Overlay not appearing above fullscreen applications
- Make sure you're running the application as administrator
- Some fullscreen applications may override the always-on-top behavior

### Hotkeys not working
- Check if other applications are using the same hotkey combinations
- Modify the hotkeys in the config.json file
- Restart the application after changing configuration

### High CPU usage
- Increase the `animation_speed` value in config.json to reduce update frequency
- Close the statistics window when not needed

### Permission errors
- Run the application as administrator if you encounter permission issues
- Some antivirus software may block global keyboard monitoring

## Performance

The application is designed to be resource-efficient:
- Minimal CPU usage when idle
- Low memory footprint
- Efficient rendering using tkinter's built-in optimization
- Background threads for non-blocking operations

## Development

The Rust codebase is modular and well-structured:

- `src/main.rs`: Main application entry point and orchestration
- `src/config.rs`: Configuration management with JSON serialization
- `src/overlay_window.rs`: Windows API overlay window implementation
- `src/text_animator.rs`: Text display and animation logic
- `src/input_handler.rs`: Global keyboard input processing
- `src/session_manager.rs`: Session lifecycle and statistics management
- `src/statistics_display.rs`: Statistics tracking and display window
- `src/hotkey_manager.rs`: Global hotkey registration and handling
- `src/word_list.rs`: Word list management and generation

### Building and Testing

```bash
# Build in debug mode
cargo build

# Build in release mode (optimized)
cargo build --release

# Run tests
cargo test

# Run with logging
RUST_LOG=debug cargo run
```

### Performance Benefits

The Rust version provides several advantages over the Python implementation:

- **Lower Memory Usage**: Rust's zero-cost abstractions and lack of garbage collection
- **Better Performance**: Native compilation and optimized async runtime
- **Improved Reliability**: Rust's type system prevents many runtime errors
- **Direct Windows API**: No intermediate layers, better integration with Windows

## License

This project is open source. Feel free to modify and distribute according to your needs.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.
