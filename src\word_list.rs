use rand::seq::SliceRandom;
use rand::thread_rng;

pub struct WordList {
    words: Vec<&'static str>,
    current_words: Vec<String>,
    current_index: usize,
}

impl WordList {
    pub fn new() -> Self {
        WordList {
            words: vec![
                // Short common words
                "the", "and", "for", "are", "but", "not", "you", "all", "can", "had", 
                "her", "was", "one", "our", "out", "day", "get", "has", "him", "his", 
                "how", "man", "new", "now", "old", "see", "two", "way", "who", "boy", 
                "did", "its", "let", "put", "say", "she", "too", "use",
                
                // Medium length words
                "about", "after", "again", "before", "being", "could", "every", "first", 
                "found", "great", "group", "house", "large", "last", "left", "life", 
                "little", "long", "made", "make", "many", "most", "move", "much", 
                "name", "never", "next", "night", "number", "other", "part", "place", 
                "point", "right", "same", "seem", "small", "sound", "still", "such", 
                "take", "than", "that", "their", "there", "these", "they", "thing", 
                "think", "this", "those", "three", "time", "very", "want", "water", 
                "well", "went", "were", "what", "where", "which", "while", "with", 
                "work", "world", "would", "write", "year", "young",
                
                // Longer words
                "against", "another", "around", "because", "become", "between", "change", 
                "children", "come", "country", "different", "does", "each", "example", 
                "follow", "good", "help", "here", "home", "important", "into", "just", 
                "know", "learn", "line", "live", "look", "means", "might", "need", 
                "only", "over", "own", "people", "play", "school", "should", "show", 
                "small", "some", "start", "state", "system", "turn", "under", "until", 
                "used", "want", "ways", "when", "will", "without", "word", "work"
            ],
            current_words: Vec::new(),
            current_index: 0,
        }
    }
    
    pub fn generate_word_sequence(&mut self, count: usize) -> Vec<String> {
        let mut rng = thread_rng();
        let actual_count = count.min(self.words.len());
        
        // Choose random words without replacement
        let selected_words: Vec<&str> = self.words
            .choose_multiple(&mut rng, actual_count)
            .cloned()
            .collect();
        
        self.current_words = selected_words.iter().map(|&s| s.to_string()).collect();
        self.current_index = 0;
        
        self.current_words.clone()
    }
    
    pub fn get_current_word(&self) -> Option<String> {
        if self.current_index < self.current_words.len() {
            Some(self.current_words[self.current_index].clone())
        } else {
            None
        }
    }
    
    pub fn next_word(&mut self) -> Option<String> {
        self.current_index += 1;
        self.get_current_word()
    }
    
    pub fn get_remaining_words(&self) -> Vec<String> {
        if self.current_index < self.current_words.len() {
            self.current_words[self.current_index..].to_vec()
        } else {
            Vec::new()
        }
    }
    
    pub fn get_all_words(&self) -> Vec<String> {
        self.current_words.clone()
    }
    
    pub fn get_current_index(&self) -> usize {
        self.current_index
    }
    
    pub fn reset(&mut self) {
        self.current_index = 0;
    }
    
    pub fn is_complete(&self) -> bool {
        self.current_index >= self.current_words.len()
    }
    
    pub fn get_progress(&self) -> (usize, usize) {
        (self.current_index, self.current_words.len())
    }
    
    pub fn get_word_at_index(&self, index: usize) -> Option<String> {
        if index < self.current_words.len() {
            Some(self.current_words[index].clone())
        } else {
            None
        }
    }
    
    pub fn set_current_index(&mut self, index: usize) {
        if index <= self.current_words.len() {
            self.current_index = index;
        }
    }
}

impl Default for WordList {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_word_list_creation() {
        let word_list = WordList::new();
        assert!(!word_list.words.is_empty());
        assert!(word_list.current_words.is_empty());
        assert_eq!(word_list.current_index, 0);
    }
    
    #[test]
    fn test_generate_word_sequence() {
        let mut word_list = WordList::new();
        let words = word_list.generate_word_sequence(5);
        
        assert_eq!(words.len(), 5);
        assert_eq!(word_list.current_words.len(), 5);
        assert_eq!(word_list.current_index, 0);
        
        // All generated words should be from the original word list
        for word in &words {
            assert!(word_list.words.contains(&word.as_str()));
        }
    }
    
    #[test]
    fn test_word_navigation() {
        let mut word_list = WordList::new();
        word_list.generate_word_sequence(3);
        
        let first_word = word_list.get_current_word().unwrap();
        assert_eq!(word_list.get_current_index(), 0);
        
        let second_word = word_list.next_word().unwrap();
        assert_eq!(word_list.get_current_index(), 1);
        assert_ne!(first_word, second_word);
        
        let third_word = word_list.next_word().unwrap();
        assert_eq!(word_list.get_current_index(), 2);
        
        let none_word = word_list.next_word();
        assert!(none_word.is_none());
        assert!(word_list.is_complete());
    }
    
    #[test]
    fn test_reset() {
        let mut word_list = WordList::new();
        word_list.generate_word_sequence(3);
        word_list.next_word();
        word_list.next_word();
        
        assert_eq!(word_list.get_current_index(), 2);
        
        word_list.reset();
        assert_eq!(word_list.get_current_index(), 0);
        assert!(!word_list.is_complete());
    }
    
    #[test]
    fn test_get_remaining_words() {
        let mut word_list = WordList::new();
        word_list.generate_word_sequence(5);
        
        let remaining = word_list.get_remaining_words();
        assert_eq!(remaining.len(), 5);
        
        word_list.next_word();
        word_list.next_word();
        
        let remaining = word_list.get_remaining_words();
        assert_eq!(remaining.len(), 3);
    }
    
    #[test]
    fn test_progress() {
        let mut word_list = WordList::new();
        word_list.generate_word_sequence(10);
        
        let (current, total) = word_list.get_progress();
        assert_eq!(current, 0);
        assert_eq!(total, 10);
        
        word_list.next_word();
        word_list.next_word();
        
        let (current, total) = word_list.get_progress();
        assert_eq!(current, 2);
        assert_eq!(total, 10);
    }
}
