mod config;
mod word_list;
mod overlay_window;
mod text_animator;
mod input_handler;
mod statistics_display;
mod hotkey_manager;
mod session_manager;

use std::sync::Arc;
use tokio::sync::Mutex;

use config::Config;
use word_list::WordList;
use overlay_window::OverlayWindow;
use text_animator::TextAnimator;
use input_handler::SimpleInputHandler;
use statistics_display::{StatisticsDisplay, StatisticsTracker};
use hotkey_manager::HotkeyManager;
use session_manager::SessionManager;

pub struct TypewriterOverlay {
    config: Arc<Config>,
    word_list: Arc<Mutex<WordList>>,
    session_manager: Arc<Mutex<SessionManager>>,
    statistics_tracker: Arc<Mutex<StatisticsTracker>>,
    overlay_window: Option<Arc<Mutex<OverlayWindow>>>,
    text_animator: Option<Arc<Mutex<TextAnimator>>>,
    statistics_display: Option<Arc<Mutex<StatisticsDisplay>>>,
    input_handler: Option<Arc<Mutex<SimpleInputHandler>>>,
    hotkey_manager: Option<Arc<Mutex<HotkeyManager>>>,
    is_running: Arc<Mutex<bool>>,
}

impl TypewriterOverlay {
    pub fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let config = Arc::new(Config::load()?);
        let word_list = Arc::new(Mutex::new(WordList::new()));
        let session_manager = Arc::new(Mutex::new(SessionManager::new(config.clone())));
        let statistics_tracker = Arc::new(Mutex::new(StatisticsTracker::new()));

        Ok(TypewriterOverlay {
            config,
            word_list,
            session_manager,
            statistics_tracker,
            overlay_window: None,
            text_animator: None,
            statistics_display: None,
            input_handler: None,
            hotkey_manager: None,
            is_running: Arc::new(Mutex::new(false)),
        })
    }

    pub async fn initialize(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Initialize components in order
        println!("Initializing Typewriter Overlay...");

        // Create overlay window
        let overlay_window = Arc::new(Mutex::new(OverlayWindow::new(self.config.clone())?));
        overlay_window.lock().await.create_window()?;
        self.overlay_window = Some(overlay_window.clone());

        // Create text animator
        let text_animator = Arc::new(Mutex::new(TextAnimator::new(
            overlay_window.clone(),
            self.config.clone(),
        )));
        self.text_animator = Some(text_animator.clone());

        // Create statistics display
        let statistics_display = Arc::new(Mutex::new(StatisticsDisplay::new(
            self.config.clone(),
            self.session_manager.clone(),
        )?));
        statistics_display.lock().await.create_window()?;
        self.statistics_display = Some(statistics_display.clone());

        // Create input handler
        let input_handler = Arc::new(Mutex::new(SimpleInputHandler::new(
            text_animator.clone(),
            self.session_manager.clone(),
        )));
        self.input_handler = Some(input_handler.clone());

        // Create hotkey manager
        let hotkey_manager = Arc::new(Mutex::new(HotkeyManager::new(self.config.clone())?));

        // Register hotkey callbacks
        {
            let overlay_window_clone = overlay_window.clone();
            let mut hm = hotkey_manager.lock().await;
            hm.register_toggle_callback(move || {
                // Toggle overlay visibility
                tokio::spawn(async move {
                    let mut overlay = overlay_window_clone.lock().await;
                    overlay.toggle_visibility();
                });
            });
        }

        {
            let statistics_display_clone = statistics_display.clone();
            let mut hm = hotkey_manager.lock().await;
            hm.register_stats_callback(move || {
                // Toggle statistics visibility
                tokio::spawn(async move {
                    let mut stats = statistics_display_clone.lock().await;
                    stats.toggle_visibility();
                });
            });
        }

        self.hotkey_manager = Some(hotkey_manager.clone());

        Ok(())
    }

    pub async fn start(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        self.initialize().await?;

        *self.is_running.lock().await = true;

        println!("Typewriter Overlay started!");
        println!("Toggle overlay: {}", self.config.toggle_hotkey);
        println!("Show statistics: {}", self.config.stats_hotkey);
        println!("Press Ctrl+C to exit");

        // Start components
        if let Some(hotkey_manager) = &self.hotkey_manager {
            hotkey_manager.lock().await.start().await?;
        }

        if let Some(input_handler) = &self.input_handler {
            input_handler.lock().await.start_listening().await?;
        }

        // Main event loop
        self.run_main_loop().await?;

        Ok(())
    }

    async fn run_main_loop(&self) -> Result<(), Box<dyn std::error::Error>> {
        while *self.is_running.lock().await {
            // Update overlay window
            if let Some(overlay_window) = &self.overlay_window {
                overlay_window.lock().await.update().await?;
            }

            // Update statistics display
            if let Some(statistics_display) = &self.statistics_display {
                statistics_display.lock().await.update().await?;
            }

            // Small delay to prevent excessive CPU usage
            tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
        }

        Ok(())
    }

    pub async fn start_new_session(&self) -> Result<(), Box<dyn std::error::Error>> {
        // Generate new words
        let words = {
            let mut word_list = self.word_list.lock().await;
            word_list.generate_word_sequence(self.config.words_per_line)
        };

        // Set words in text animator
        if let Some(text_animator) = &self.text_animator {
            let mut animator = text_animator.lock().await;
            animator.set_words(words).await;
        }

        // Reset session manager
        {
            let mut session_manager = self.session_manager.lock().await;
            if session_manager.is_session_active() {
                // Save current session to tracker
                let stats = session_manager.get_statistics();
                let mut tracker = self.statistics_tracker.lock().await;
                tracker.add_session(stats);
                session_manager.end_session();
            }
            session_manager.start_session();
        }

        // Activate input handler
        if let Some(input_handler) = &self.input_handler {
            let mut handler = input_handler.lock().await;
            handler.activate();
        }

        // Show overlay
        if let Some(overlay_window) = &self.overlay_window {
            let mut overlay = overlay_window.lock().await;
            overlay.show();
        }

        Ok(())
    }

    pub async fn stop(&mut self) {
        *self.is_running.lock().await = false;

        // Stop all components
        if let Some(hotkey_manager) = &self.hotkey_manager {
            hotkey_manager.lock().await.stop().await;
        }

        if let Some(input_handler) = &self.input_handler {
            input_handler.lock().await.stop_listening().await;
        }

        println!("Typewriter Overlay stopped.");
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Check if running on Windows
    if !cfg!(windows) {
        eprintln!("This application is designed for Windows only.");
        return Err("Unsupported platform".into());
    }

    let mut app = TypewriterOverlay::new()?;

    // Handle Ctrl+C gracefully
    let is_running = app.is_running.clone();
    tokio::spawn(async move {
        tokio::signal::ctrl_c().await.expect("Failed to listen for ctrl_c");
        println!("\nShutting down...");
        *is_running.lock().await = false;
    });

    match app.start().await {
        Ok(_) => {
            app.stop().await;
            Ok(())
        }
        Err(e) => {
            eprintln!("Fatal error: {}", e);
            app.stop().await;
            Err(e)
        }
    }
}
