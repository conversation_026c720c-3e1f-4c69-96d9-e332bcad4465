from pynput import keyboard
import time
import threading

class InputHandler:
    def __init__(self, text_animator, session_manager):
        self.text_animator = text_animator
        self.session_manager = session_manager
        self.listener = None
        self.is_active = False
        self.last_input_time = 0
        
        # Keys to ignore (modifier keys, etc.)
        self.ignore_keys = {
            keyboard.Key.ctrl_l, keyboard.Key.ctrl_r,
            keyboard.Key.alt_l, keyboard.Key.alt_r,
            keyboard.Key.shift_l, keyboard.Key.shift_r,
            keyboard.Key.cmd, keyboard.Key.tab,
            keyboard.Key.caps_lock, keyboard.Key.esc,
            keyboard.Key.f1, keyboard.Key.f2, keyboard.Key.f3, keyboard.Key.f4,
            keyboard.Key.f5, keyboard.Key.f6, keyboard.Key.f7, keyboard.Key.f8,
            keyboard.Key.f9, keyboard.Key.f10, keyboard.Key.f11, keyboard.Key.f12,
            keyboard.Key.insert, keyboard.Key.delete, keyboard.Key.home,
            keyboard.Key.end, keyboard.Key.page_up, keyboard.Key.page_down,
            keyboard.Key.up, keyboard.Key.down, keyboard.Key.left, keyboard.Key.right,
            keyboard.Key.print_screen, keyboard.Key.scroll_lock, keyboard.Key.pause
        }
    
    def start_listening(self):
        """Start listening for keyboard input"""
        if not self.listener:
            self.listener = keyboard.Listener(
                on_press=self._on_key_press,
                suppress=False  # Don't suppress keys, just monitor
            )
            self.listener.start()
    
    def stop_listening(self):
        """Stop listening for keyboard input"""
        if self.listener:
            self.listener.stop()
            self.listener = None
    
    def activate(self):
        """Activate input handling"""
        self.is_active = True
        self.last_input_time = time.time()
    
    def deactivate(self):
        """Deactivate input handling"""
        self.is_active = False
    
    def _on_key_press(self, key):
        """Handle key press events"""
        if not self.is_active:
            return
        
        try:
            # Update last input time
            self.last_input_time = time.time()
            
            # Ignore certain keys
            if key in self.ignore_keys:
                return
            
            # Handle special keys
            if key == keyboard.Key.backspace:
                self._handle_backspace()
                return
            elif key == keyboard.Key.space:
                self._handle_character(' ')
                return
            elif key == keyboard.Key.enter:
                # Enter key might be used to restart or skip
                return
            
            # Handle regular characters
            if hasattr(key, 'char') and key.char:
                self._handle_character(key.char)
                
        except Exception as e:
            # Silently handle any errors to prevent crashes
            pass
    
    def _handle_character(self, char):
        """Handle a regular character input"""
        if not char or not char.isprintable():
            return
            
        # Start session if not already started
        if not self.session_manager.is_session_active():
            self.session_manager.start_session()
        
        # Update session activity
        self.session_manager.update_activity()
        
        # Add character to text animator
        success = self.text_animator.add_character(char)
        
        # Track statistics
        current_word = self.text_animator.get_current_word()
        typed_text = self.text_animator.get_typed_text()
        
        if current_word and typed_text:
            # Check if character is correct
            expected_char = current_word[len(typed_text) - 1] if len(typed_text) <= len(current_word) else None
            is_correct = (expected_char == char)
            
            # Update session statistics
            self.session_manager.add_keystroke(char, is_correct)
        
        # Check if all words are completed
        if self.text_animator.is_complete():
            self._handle_completion()
    
    def _handle_backspace(self):
        """Handle backspace key"""
        # Update session activity
        if self.session_manager.is_session_active():
            self.session_manager.update_activity()
        
        # Remove character from text animator
        success = self.text_animator.remove_character()
        
        if success:
            # Track backspace in statistics
            self.session_manager.add_backspace()
    
    def _handle_completion(self):
        """Handle completion of all words"""
        # End current session
        if self.session_manager.is_session_active():
            self.session_manager.end_session()
        
        # Could trigger new word sequence here
        # For now, just deactivate
        self.deactivate()
    
    def get_last_input_time(self):
        """Get the time of the last input"""
        return self.last_input_time
    
    def is_input_recent(self, timeout_seconds):
        """Check if input was recent within timeout"""
        if self.last_input_time == 0:
            return False
        return (time.time() - self.last_input_time) < timeout_seconds


class SessionManager:
    def __init__(self, config):
        self.config = config
        self.session_active = False
        self.session_start_time = 0
        self.session_end_time = 0
        self.total_keystrokes = 0
        self.correct_keystrokes = 0
        self.incorrect_keystrokes = 0
        self.backspaces = 0
        self.words_completed = 0
        self.last_activity_time = 0
        
        # Thread for monitoring session timeout
        self.timeout_thread = None
        self.should_monitor = False
    
    def start_session(self):
        """Start a new typing session"""
        self.session_active = True
        self.session_start_time = time.time()
        self.session_end_time = 0
        self.total_keystrokes = 0
        self.correct_keystrokes = 0
        self.incorrect_keystrokes = 0
        self.backspaces = 0
        self.words_completed = 0
        self.last_activity_time = time.time()
        
        # Start monitoring for timeout
        self._start_timeout_monitor()
    
    def end_session(self):
        """End the current typing session"""
        if self.session_active:
            self.session_end_time = time.time()
            self.session_active = False
            self._stop_timeout_monitor()
    
    def update_activity(self):
        """Update the last activity time"""
        self.last_activity_time = time.time()
    
    def add_keystroke(self, char, is_correct):
        """Add a keystroke to the session statistics"""
        if not self.session_active:
            return
            
        self.total_keystrokes += 1
        if is_correct:
            self.correct_keystrokes += 1
        else:
            self.incorrect_keystrokes += 1
    
    def add_backspace(self):
        """Add a backspace to the session statistics"""
        if self.session_active:
            self.backspaces += 1
    
    def add_completed_word(self):
        """Add a completed word to the session statistics"""
        if self.session_active:
            self.words_completed += 1
    
    def is_session_active(self):
        """Check if a session is currently active"""
        return self.session_active
    
    def get_session_duration(self):
        """Get the duration of the current or last session"""
        if self.session_active:
            return time.time() - self.session_start_time
        elif self.session_end_time > 0:
            return self.session_end_time - self.session_start_time
        return 0
    
    def get_wpm(self):
        """Calculate words per minute"""
        duration_minutes = self.get_session_duration() / 60
        if duration_minutes > 0:
            return self.words_completed / duration_minutes
        return 0
    
    def get_accuracy(self):
        """Calculate typing accuracy percentage"""
        if self.total_keystrokes > 0:
            return (self.correct_keystrokes / self.total_keystrokes) * 100
        return 0
    
    def get_statistics(self):
        """Get comprehensive session statistics"""
        return {
            'active': self.session_active,
            'duration': self.get_session_duration(),
            'wpm': self.get_wpm(),
            'accuracy': self.get_accuracy(),
            'total_keystrokes': self.total_keystrokes,
            'correct_keystrokes': self.correct_keystrokes,
            'incorrect_keystrokes': self.incorrect_keystrokes,
            'backspaces': self.backspaces,
            'words_completed': self.words_completed
        }
    
    def _start_timeout_monitor(self):
        """Start monitoring for session timeout"""
        self.should_monitor = True
        self.timeout_thread = threading.Thread(target=self._monitor_timeout, daemon=True)
        self.timeout_thread.start()
    
    def _stop_timeout_monitor(self):
        """Stop monitoring for session timeout"""
        self.should_monitor = False
        if self.timeout_thread:
            self.timeout_thread.join(timeout=1)
    
    def _monitor_timeout(self):
        """Monitor for session timeout in a separate thread"""
        timeout_seconds = self.config.get('idle_timeout')
        
        while self.should_monitor and self.session_active:
            time.sleep(0.5)  # Check every 500ms
            
            if (time.time() - self.last_activity_time) > timeout_seconds:
                # Session timed out
                self.end_session()
                break
