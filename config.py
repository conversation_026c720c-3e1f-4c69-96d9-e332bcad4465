# Configuration settings for the typewriter overlay
import json
import os

class Config:
    def __init__(self):
        self.config_file = "config.json"
        self.default_config = {
            "idle_timeout": 3.0,  # seconds
            "toggle_hotkey": "ctrl+shift+t",
            "stats_hotkey": "ctrl+shift+s",
            "font_size": 24,
            "font_family": "Consolas",
            "text_color": "#FFFFFF",
            "background_color": "#000000",
            "window_opacity": 0.8,
            "animation_speed": 50,  # milliseconds
            "words_per_line": 10
        }
        self.config = self.load_config()
    
    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                # Merge with defaults to ensure all keys exist
                config = self.default_config.copy()
                config.update(loaded_config)
                return config
            except (json.JSONDecodeError, IOError):
                return self.default_config.copy()
        return self.default_config.copy()
    
    def save_config(self):
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=2)
        except IOError:
            pass  # Fail silently if can't save
    
    def get(self, key):
        return self.config.get(key, self.default_config.get(key))
    
    def set(self, key, value):
        self.config[key] = value
        self.save_config()
