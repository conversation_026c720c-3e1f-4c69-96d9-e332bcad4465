#!/usr/bin/env python3
"""
Simple test version of the typewriter overlay without external dependencies
"""

import tkinter as tk
import time
import threading
import json

class SimpleOverlay:
    def __init__(self):
        self.root = None
        self.canvas = None
        self.is_visible = False
        self.words = ["hello", "world", "typing", "test", "overlay"]
        self.current_word_index = 0
        self.typed_chars = ""
        
    def create_window(self):
        """Create a simple overlay window"""
        self.root = tk.Tk()
        self.root.title("Typewriter Overlay Test")
        self.root.attributes('-topmost', True)
        self.root.configure(bg='black')
        
        # Set window size and position
        window_width = 800
        window_height = 200
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        
        # Create canvas
        self.canvas = tk.Canvas(
            self.root,
            width=window_width,
            height=window_height,
            bg='black',
            highlightthickness=0
        )
        self.canvas.pack()
        
        # Bind keyboard events
        self.root.bind('<KeyPress>', self.on_key_press)
        self.root.focus_set()
        
        # Display initial words
        self.display_words()
        
        return self.root
    
    def display_words(self):
        """Display the current words"""
        self.canvas.delete("all")
        
        x_start = 400  # Center of 800px window
        y = 100  # Center of 200px window
        
        for i, word in enumerate(self.words):
            if i < self.current_word_index:
                color = "gray"  # Completed words
            elif i == self.current_word_index:
                color = "white"  # Current word
            else:
                color = "lightgray"  # Future words
            
            x = x_start + (i - self.current_word_index) * 100
            
            self.canvas.create_text(
                x, y,
                text=word,
                fill=color,
                font=("Consolas", 20),
                anchor="center"
            )
        
        # Show typed characters for current word
        if self.current_word_index < len(self.words):
            current_word = self.words[self.current_word_index]
            if self.typed_chars:
                is_correct = current_word.startswith(self.typed_chars)
                color = "green" if is_correct else "red"
                
                self.canvas.create_text(
                    x_start, y + 30,
                    text=f"Typed: {self.typed_chars}",
                    fill=color,
                    font=("Consolas", 14),
                    anchor="center"
                )
    
    def on_key_press(self, event):
        """Handle key press events"""
        if self.current_word_index >= len(self.words):
            return
        
        current_word = self.words[self.current_word_index]
        
        if event.keysym == 'BackSpace':
            if self.typed_chars:
                self.typed_chars = self.typed_chars[:-1]
        elif event.keysym == 'space':
            if self.typed_chars == current_word:
                # Word completed correctly
                self.current_word_index += 1
                self.typed_chars = ""
                
                if self.current_word_index >= len(self.words):
                    self.canvas.create_text(
                        400, 150,
                        text="All words completed!",
                        fill="yellow",
                        font=("Consolas", 16),
                        anchor="center"
                    )
        elif event.char and event.char.isprintable():
            self.typed_chars += event.char
        
        self.display_words()
    
    def run(self):
        """Run the overlay"""
        if self.create_window():
            print("Simple Typewriter Overlay Test")
            print("Type the words shown on screen")
            print("Press Space after each word")
            print("Press Ctrl+C in terminal to exit")
            
            try:
                self.root.mainloop()
            except KeyboardInterrupt:
                print("\nExiting...")
                if self.root:
                    self.root.destroy()

def main():
    """Main entry point"""
    overlay = SimpleOverlay()
    overlay.run()

if __name__ == "__main__":
    main()
