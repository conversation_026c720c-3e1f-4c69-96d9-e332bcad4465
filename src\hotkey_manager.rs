use std::sync::Arc;
use std::collections::HashMap;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use winapi::um::winuser::*;
use winapi::shared::minwindef::*;

use crate::config::Config;

pub type HotkeyCallback = Arc<dyn Fn() + Send + Sync>;

pub struct HotkeyManager {
    config: Arc<Config>,
    hotkeys: HashMap<i32, HotkeyInfo>,
    callbacks: HashMap<String, HotkeyCallback>,
    is_running: bool,
    last_toggle_time: Instant,
    last_stats_time: Instant,
    debounce_duration: Duration,
}

#[derive(Debug, Clone)]
struct HotkeyInfo {
    id: i32,
    modifiers: u32,
    vk_code: u32,
    name: String,
}

impl HotkeyManager {
    pub fn new(config: Arc<Config>) -> Result<Self, Box<dyn std::error::Error>> {
        Ok(HotkeyManager {
            config,
            hotkeys: HashMap::new(),
            callbacks: HashMap::new(),
            is_running: false,
            last_toggle_time: Instant::now(),
            last_stats_time: Instant::now(),
            debounce_duration: Duration::from_millis(500),
        })
    }
    
    pub fn register_toggle_callback<F>(&mut self, callback: F)
    where
        F: Fn() + Send + Sync + 'static,
    {
        self.callbacks.insert("toggle".to_string(), Arc::new(callback));
    }
    
    pub fn register_stats_callback<F>(&mut self, callback: F)
    where
        F: Fn() + Send + Sync + 'static,
    {
        self.callbacks.insert("stats".to_string(), Arc::new(callback));
    }
    
    pub async fn start(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        if self.is_running {
            return Ok(());
        }
        
        // Parse and register hotkeys
        self.register_hotkeys()?;
        self.is_running = true;
        
        // Start message loop in a separate task
        let hotkeys = self.hotkeys.clone();
        let callbacks = self.callbacks.clone();
        let debounce_duration = self.debounce_duration;
        
        tokio::spawn(async move {
            Self::message_loop(hotkeys, callbacks, debounce_duration).await;
        });
        
        Ok(())
    }
    
    pub async fn stop(&mut self) {
        if !self.is_running {
            return;
        }
        
        self.is_running = false;
        
        // Unregister all hotkeys
        unsafe {
            for hotkey_info in self.hotkeys.values() {
                UnregisterHotKey(std::ptr::null_mut(), hotkey_info.id);
            }
        }
        
        self.hotkeys.clear();
    }
    
    fn register_hotkeys(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // Register toggle hotkey
        if let Ok((modifiers, vk_code)) = self.parse_hotkey(&self.config.toggle_hotkey) {
            let hotkey_info = HotkeyInfo {
                id: 1,
                modifiers,
                vk_code,
                name: "toggle".to_string(),
            };
            
            unsafe {
                if RegisterHotKey(std::ptr::null_mut(), hotkey_info.id, modifiers, vk_code) == 0 {
                    return Err(format!("Failed to register toggle hotkey: {}", self.config.toggle_hotkey).into());
                }
            }
            
            self.hotkeys.insert(hotkey_info.id, hotkey_info);
        }
        
        // Register stats hotkey
        if let Ok((modifiers, vk_code)) = self.parse_hotkey(&self.config.stats_hotkey) {
            let hotkey_info = HotkeyInfo {
                id: 2,
                modifiers,
                vk_code,
                name: "stats".to_string(),
            };
            
            unsafe {
                if RegisterHotKey(std::ptr::null_mut(), hotkey_info.id, modifiers, vk_code) == 0 {
                    return Err(format!("Failed to register stats hotkey: {}", self.config.stats_hotkey).into());
                }
            }
            
            self.hotkeys.insert(hotkey_info.id, hotkey_info);
        }
        
        Ok(())
    }
    
    fn parse_hotkey(&self, hotkey_str: &str) -> Result<(u32, u32), Box<dyn std::error::Error>> {
        let parts: Vec<&str> = hotkey_str.to_lowercase().split('+').collect();
        let mut modifiers = 0u32;
        let mut vk_code = 0u32;
        
        for part in &parts {
            match part.trim() {
                "ctrl" | "control" => modifiers |= MOD_CONTROL,
                "shift" => modifiers |= MOD_SHIFT,
                "alt" => modifiers |= MOD_ALT,
                "win" | "windows" => modifiers |= MOD_WIN,
                key => {
                    if vk_code != 0 {
                        return Err(format!("Multiple keys specified in hotkey: {}", hotkey_str).into());
                    }
                    vk_code = self.key_to_vk_code(key)?;
                }
            }
        }
        
        if vk_code == 0 {
            return Err(format!("No key specified in hotkey: {}", hotkey_str).into());
        }
        
        Ok((modifiers, vk_code))
    }
    
    fn key_to_vk_code(&self, key: &str) -> Result<u32, Box<dyn std::error::Error>> {
        let vk_code = match key {
            // Letters
            "a" => VK_A,
            "b" => VK_B,
            "c" => VK_C,
            "d" => VK_D,
            "e" => VK_E,
            "f" => VK_F,
            "g" => VK_G,
            "h" => VK_H,
            "i" => VK_I,
            "j" => VK_J,
            "k" => VK_K,
            "l" => VK_L,
            "m" => VK_M,
            "n" => VK_N,
            "o" => VK_O,
            "p" => VK_P,
            "q" => VK_Q,
            "r" => VK_R,
            "s" => VK_S,
            "t" => VK_T,
            "u" => VK_U,
            "v" => VK_V,
            "w" => VK_W,
            "x" => VK_X,
            "y" => VK_Y,
            "z" => VK_Z,
            
            // Numbers
            "0" => VK_0,
            "1" => VK_1,
            "2" => VK_2,
            "3" => VK_3,
            "4" => VK_4,
            "5" => VK_5,
            "6" => VK_6,
            "7" => VK_7,
            "8" => VK_8,
            "9" => VK_9,
            
            // Function keys
            "f1" => VK_F1,
            "f2" => VK_F2,
            "f3" => VK_F3,
            "f4" => VK_F4,
            "f5" => VK_F5,
            "f6" => VK_F6,
            "f7" => VK_F7,
            "f8" => VK_F8,
            "f9" => VK_F9,
            "f10" => VK_F10,
            "f11" => VK_F11,
            "f12" => VK_F12,
            
            // Special keys
            "space" => VK_SPACE,
            "enter" | "return" => VK_RETURN,
            "tab" => VK_TAB,
            "escape" | "esc" => VK_ESCAPE,
            "backspace" => VK_BACK,
            "delete" | "del" => VK_DELETE,
            "insert" | "ins" => VK_INSERT,
            "home" => VK_HOME,
            "end" => VK_END,
            "pageup" | "pgup" => VK_PRIOR,
            "pagedown" | "pgdn" => VK_NEXT,
            "up" => VK_UP,
            "down" => VK_DOWN,
            "left" => VK_LEFT,
            "right" => VK_RIGHT,
            
            _ => return Err(format!("Unknown key: {}", key).into()),
        };
        
        Ok(vk_code as u32)
    }
    
    async fn message_loop(
        hotkeys: HashMap<i32, HotkeyInfo>,
        callbacks: HashMap<String, HotkeyCallback>,
        debounce_duration: Duration,
    ) {
        let mut last_toggle_time = Instant::now();
        let mut last_stats_time = Instant::now();
        
        unsafe {
            let mut msg = MSG {
                hwnd: std::ptr::null_mut(),
                message: 0,
                wParam: 0,
                lParam: 0,
                time: 0,
                pt: POINT { x: 0, y: 0 },
            };
            
            loop {
                let result = GetMessageW(&mut msg, std::ptr::null_mut(), 0, 0);
                
                if result == 0 || result == -1 {
                    // WM_QUIT or error
                    break;
                }
                
                if msg.message == WM_HOTKEY {
                    let hotkey_id = msg.wParam as i32;
                    
                    if let Some(hotkey_info) = hotkeys.get(&hotkey_id) {
                        let now = Instant::now();
                        
                        match hotkey_info.name.as_str() {
                            "toggle" => {
                                if now.duration_since(last_toggle_time) > debounce_duration {
                                    if let Some(callback) = callbacks.get("toggle") {
                                        callback();
                                    }
                                    last_toggle_time = now;
                                }
                            }
                            "stats" => {
                                if now.duration_since(last_stats_time) > debounce_duration {
                                    if let Some(callback) = callbacks.get("stats") {
                                        callback();
                                    }
                                    last_stats_time = now;
                                }
                            }
                            _ => {}
                        }
                    }
                }
                
                TranslateMessage(&msg);
                DispatchMessageW(&msg);
                
                // Small delay to prevent excessive CPU usage
                tokio::time::sleep(Duration::from_millis(1)).await;
            }
        }
    }
}

impl Drop for HotkeyManager {
    fn drop(&mut self) {
        // Unregister all hotkeys
        unsafe {
            for hotkey_info in self.hotkeys.values() {
                UnregisterHotKey(std::ptr::null_mut(), hotkey_info.id);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_hotkey_parsing() {
        let config = Arc::new(Config::default());
        let hotkey_manager = HotkeyManager::new(config).unwrap();
        
        // Test valid hotkey
        let result = hotkey_manager.parse_hotkey("ctrl+shift+t");
        assert!(result.is_ok());
        let (modifiers, vk_code) = result.unwrap();
        assert_eq!(modifiers, MOD_CONTROL | MOD_SHIFT);
        assert_eq!(vk_code, VK_T as u32);
        
        // Test invalid hotkey
        let result = hotkey_manager.parse_hotkey("invalid+key");
        assert!(result.is_err());
    }
    
    #[test]
    fn test_key_to_vk_code() {
        let config = Arc::new(Config::default());
        let hotkey_manager = HotkeyManager::new(config).unwrap();
        
        assert_eq!(hotkey_manager.key_to_vk_code("a").unwrap(), VK_A as u32);
        assert_eq!(hotkey_manager.key_to_vk_code("space").unwrap(), VK_SPACE as u32);
        assert_eq!(hotkey_manager.key_to_vk_code("f1").unwrap(), VK_F1 as u32);
        
        assert!(hotkey_manager.key_to_vk_code("invalid").is_err());
    }
}
