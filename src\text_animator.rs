use std::sync::Arc;
use tokio::sync::Mutex;

use crate::config::Config;
use crate::overlay_window::OverlayWindow;

#[derive(Debug, Clone)]
pub struct WordObject {
    pub text: String,
    pub x: i32,
    pub y: i32,
    pub width: i32,
    pub index: usize,
    pub completed: bool,
}

pub struct TextAnimator {
    overlay_window: Arc<Mutex<OverlayWindow>>,
    config: Arc<Config>,
    words: Vec<String>,
    word_objects: Vec<WordObject>,
    current_word_index: usize,
    typed_chars: String,
    word_spacing: i32,
}

impl TextAnimator {
    pub fn new(overlay_window: Arc<Mutex<OverlayWindow>>, config: Arc<Config>) -> Self {
        TextAnimator {
            overlay_window,
            config,
            words: Vec::new(),
            word_objects: Vec::new(),
            current_word_index: 0,
            typed_chars: String::new(),
            word_spacing: 20, // Default spacing between words
        }
    }
    
    pub async fn set_words(&mut self, words: Vec<String>) {
        self.words = words;
        self.current_word_index = 0;
        self.typed_chars.clear();
        self.create_word_objects().await;
        self.render_words().await;
    }
    
    async fn create_word_objects(&mut self) {
        self.word_objects.clear();
        
        if self.words.is_empty() {
            return;
        }
        
        let overlay = self.overlay_window.lock().await;
        let (window_width, window_height) = overlay.get_window_dimensions();
        let (center_x, center_y) = overlay.get_center_position();
        drop(overlay);
        
        // Calculate word widths (approximate)
        let mut word_widths = Vec::new();
        for word in &self.words {
            // Rough estimation: each character is about 60% of font size in width
            let estimated_width = (word.len() as i32) * (self.config.font_size as i32 * 6 / 10);
            word_widths.push(estimated_width);
        }
        
        // Position words starting from center
        let mut current_x = center_x;
        
        for (i, word) in self.words.iter().enumerate() {
            let word_obj = WordObject {
                text: word.clone(),
                x: current_x,
                y: center_y,
                width: word_widths[i],
                index: i,
                completed: i < self.current_word_index,
            };
            
            self.word_objects.push(word_obj);
            
            // Move to next word position
            current_x += word_widths[i] + self.word_spacing;
        }
    }
    
    async fn render_words(&self) {
        let overlay = self.overlay_window.lock().await;
        
        // Clear canvas first
        overlay.clear_canvas();
        
        // Draw each word with appropriate color
        for word_obj in &self.word_objects {
            let color = if word_obj.index < self.current_word_index {
                "#888888" // Completed words (gray)
            } else if word_obj.index == self.current_word_index {
                &self.config.text_color // Current word (white)
            } else {
                "#CCCCCC" // Future words (light gray)
            };
            
            if let Err(e) = overlay.draw_text(&word_obj.text, word_obj.x, word_obj.y, Some(color)) {
                eprintln!("Error drawing text: {}", e);
            }
        }
        
        // If we're in the middle of typing a word, show the typed portion
        if !self.typed_chars.is_empty() && self.current_word_index < self.words.len() {
            if let Some(current_word_obj) = self.word_objects.get(self.current_word_index) {
                // Draw typed portion in a different color (e.g., green for correct)
                let typed_color = if self.is_current_typing_correct() {
                    "#00FF00" // Green for correct
                } else {
                    "#FF0000" // Red for incorrect
                };
                
                if let Err(e) = overlay.draw_text(&self.typed_chars, current_word_obj.x, current_word_obj.y, Some(typed_color)) {
                    eprintln!("Error drawing typed text: {}", e);
                }
            }
        }
    }
    
    pub async fn add_character(&mut self, ch: char) -> bool {
        if self.current_word_index >= self.words.len() {
            return false;
        }
        
        let current_word = &self.words[self.current_word_index];
        
        if ch == ' ' {
            // Space pressed - check if current word is complete
            if self.typed_chars == *current_word {
                self.complete_current_word().await;
                return true;
            } else {
                // Word not complete, ignore space or handle as error
                return false;
            }
        } else {
            // Regular character
            self.typed_chars.push(ch);
            self.render_words().await;
            
            // Check if word is complete (but wait for space to move to next)
            if self.typed_chars == *current_word {
                // Word completed, but wait for space to move to next
            } else if self.typed_chars.len() > current_word.len() {
                // Too many characters, handle error
                // For now, just truncate
                self.typed_chars.truncate(current_word.len());
            }
            
            return true;
        }
    }
    
    pub async fn remove_character(&mut self) -> bool {
        if !self.typed_chars.is_empty() {
            self.typed_chars.pop();
            self.render_words().await;
            true
        } else {
            false
        }
    }
    
    async fn complete_current_word(&mut self) {
        if self.current_word_index < self.words.len() {
            // Mark current word as completed
            if let Some(word_obj) = self.word_objects.get_mut(self.current_word_index) {
                word_obj.completed = true;
            }
            
            // Move to next word
            self.current_word_index += 1;
            self.typed_chars.clear();
            
            // Animate words moving left
            self.animate_words_left().await;
            
            // Re-render
            self.render_words().await;
        }
    }
    
    async fn animate_words_left(&mut self) {
        if self.word_objects.is_empty() || self.current_word_index == 0 {
            return;
        }
        
        // Calculate how much to move (width of completed word + spacing)
        let completed_word = &self.word_objects[self.current_word_index - 1];
        let move_distance = completed_word.width + self.word_spacing;
        
        // Move all words left
        for word_obj in &mut self.word_objects {
            word_obj.x -= move_distance;
        }
    }
    
    pub fn get_current_word(&self) -> Option<String> {
        if self.current_word_index < self.words.len() {
            Some(self.words[self.current_word_index].clone())
        } else {
            None
        }
    }
    
    pub fn get_typed_text(&self) -> String {
        self.typed_chars.clone()
    }
    
    pub fn is_complete(&self) -> bool {
        self.current_word_index >= self.words.len()
    }
    
    pub fn get_progress(&self) -> (usize, usize) {
        (self.current_word_index, self.words.len())
    }
    
    fn is_current_typing_correct(&self) -> bool {
        if self.current_word_index >= self.words.len() {
            return false;
        }
        
        let current_word = &self.words[self.current_word_index];
        
        if self.typed_chars.len() > current_word.len() {
            return false;
        }
        
        current_word.starts_with(&self.typed_chars)
    }
    
    pub async fn reset(&mut self) {
        self.current_word_index = 0;
        self.typed_chars.clear();
        self.create_word_objects().await;
        self.render_words().await;
    }
    
    pub async fn clear(&mut self) {
        self.words.clear();
        self.word_objects.clear();
        self.current_word_index = 0;
        self.typed_chars.clear();
        
        let overlay = self.overlay_window.lock().await;
        overlay.clear_canvas();
    }
    
    pub fn get_current_word_index(&self) -> usize {
        self.current_word_index
    }
    
    pub fn get_words(&self) -> Vec<String> {
        self.words.clone()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    use crate::overlay_window::OverlayWindow;
    
    #[tokio::test]
    async fn test_text_animator_creation() {
        let config = Arc::new(Config::default());
        let overlay = Arc::new(Mutex::new(OverlayWindow::new(config.clone()).unwrap()));
        let animator = TextAnimator::new(overlay, config);
        
        assert_eq!(animator.current_word_index, 0);
        assert!(animator.typed_chars.is_empty());
        assert!(animator.words.is_empty());
    }
    
    #[tokio::test]
    async fn test_set_words() {
        let config = Arc::new(Config::default());
        let overlay = Arc::new(Mutex::new(OverlayWindow::new(config.clone()).unwrap()));
        let mut animator = TextAnimator::new(overlay, config);
        
        let words = vec!["hello".to_string(), "world".to_string()];
        animator.set_words(words.clone()).await;
        
        assert_eq!(animator.words, words);
        assert_eq!(animator.word_objects.len(), 2);
        assert_eq!(animator.current_word_index, 0);
    }
    
    #[tokio::test]
    async fn test_character_input() {
        let config = Arc::new(Config::default());
        let overlay = Arc::new(Mutex::new(OverlayWindow::new(config.clone()).unwrap()));
        let mut animator = TextAnimator::new(overlay, config);
        
        let words = vec!["test".to_string()];
        animator.set_words(words).await;
        
        // Type characters
        assert!(animator.add_character('t').await);
        assert_eq!(animator.typed_chars, "t");
        
        assert!(animator.add_character('e').await);
        assert_eq!(animator.typed_chars, "te");
        
        // Remove character
        assert!(animator.remove_character().await);
        assert_eq!(animator.typed_chars, "t");
    }
}
