{"rustc": 3926191382657067107, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2241668132362809309, "path": 351709369414368244, "deps": [[1573238666360410412, "rand_chacha", false, 5657821228831505443], [18130209639506977569, "rand_core", false, 13349487511838210460]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rand-ba9b665f8eaa655f\\dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}