cargo:rerun-if-changed=build.rs
cargo:rerun-if-env-changed=WINAPI_NO_BUNDLED_LIBRARIES
cargo:rerun-if-env-changed=WINAPI_STATIC_NOBUNDLE
cargo:rustc-cfg=feature="excpt"
cargo:rustc-cfg=feature="wincontypes"
cargo:rustc-cfg=feature="minwinbase"
cargo:rustc-cfg=feature="limits"
cargo:rustc-cfg=feature="ntstatus"
cargo:rustc-cfg=feature="vadefs"
cargo:rustc-cfg=feature="ntdef"
cargo:rustc-cfg=feature="winnt"
cargo:rustc-cfg=feature="basetsd"
cargo:rustc-cfg=feature="guiddef"
cargo:rustc-cfg=feature="ktmtypes"
cargo:rustc-cfg=feature="vcruntime"
cargo:rustc-cfg=feature="minwindef"
cargo:rustc-link-lib=dylib=winapi_advapi32
cargo:rustc-link-lib=dylib=winapi_gdi32
cargo:rustc-link-lib=dylib=winapi_kernel32
cargo:rustc-link-lib=dylib=winapi_msimg32
cargo:rustc-link-lib=dylib=winapi_opengl32
cargo:rustc-link-lib=dylib=winapi_user32
cargo:rustc-link-lib=dylib=winapi_winspool
