#!/usr/bin/env python3
"""
Basic test script to verify the typewriter overlay components work
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ tkinter imported successfully")
    except ImportError as e:
        print(f"✗ tkinter import failed: {e}")
        return False
    
    try:
        from config import Config
        print("✓ config module imported successfully")
    except ImportError as e:
        print(f"✗ config import failed: {e}")
        return False
    
    try:
        from word_list import WordList
        print("✓ word_list module imported successfully")
    except ImportError as e:
        print(f"✗ word_list import failed: {e}")
        return False
    
    try:
        from overlay_window import OverlayWindow
        print("✓ overlay_window module imported successfully")
    except ImportError as e:
        print(f"✗ overlay_window import failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from config import Config
        config = Config()
        print(f"✓ Config loaded with idle_timeout: {config.get('idle_timeout')}")
        print(f"✓ Toggle hotkey: {config.get('toggle_hotkey')}")
        print(f"✓ Stats hotkey: {config.get('stats_hotkey')}")
        return True
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

def test_word_list():
    """Test word list functionality"""
    print("\nTesting word list...")
    
    try:
        from word_list import WordList
        word_list = WordList()
        words = word_list.generate_word_sequence(5)
        print(f"✓ Generated words: {words}")
        
        current_word = word_list.get_current_word()
        print(f"✓ Current word: {current_word}")
        
        return True
    except Exception as e:
        print(f"✗ Word list test failed: {e}")
        return False

def test_basic_window():
    """Test basic window creation (without showing)"""
    print("\nTesting basic window creation...")
    
    try:
        import tkinter as tk
        from config import Config
        from overlay_window import OverlayWindow
        
        config = Config()
        overlay = OverlayWindow(config)
        
        # Create window but don't show it
        root = overlay.create_window()
        print("✓ Overlay window created successfully")
        
        # Test basic drawing
        text_id = overlay.draw_text("Test", overlay.get_center_x(), overlay.get_center_y())
        print("✓ Text drawing works")
        
        # Clean up
        overlay.destroy()
        print("✓ Window cleanup successful")
        
        return True
    except Exception as e:
        print(f"✗ Basic window test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Typewriter Overlay - Basic Tests")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_config,
        test_word_list,
        test_basic_window
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("Test failed!")
        except Exception as e:
            print(f"Test crashed: {e}")
    
    print("\n" + "=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! The application should work correctly.")
        return 0
    else:
        print("✗ Some tests failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
