from pynput import keyboard
import threading
import time

class HotkeyManager:
    def __init__(self, config):
        self.config = config
        self.listener = None
        self.hotkey_callbacks = {}
        self.pressed_keys = set()
        self.is_running = False
        
        # Parse hotkey combinations
        self.toggle_hotkey = self._parse_hotkey(config.get('toggle_hotkey'))
        self.stats_hotkey = self._parse_hotkey(config.get('stats_hotkey'))
        
    def _parse_hotkey(self, hotkey_string):
        """Parse hotkey string into a set of keys"""
        if not hotkey_string:
            return set()
            
        parts = hotkey_string.lower().split('+')
        keys = set()
        
        for part in parts:
            part = part.strip()
            if part == 'ctrl':
                keys.add(keyboard.Key.ctrl_l)
                keys.add(keyboard.Key.ctrl_r)
            elif part == 'shift':
                keys.add(keyboard.Key.shift_l)
                keys.add(keyboard.Key.shift_r)
            elif part == 'alt':
                keys.add(keyboard.Key.alt_l)
                keys.add(keyboard.Key.alt_r)
            elif part == 'cmd' or part == 'win':
                keys.add(keyboard.Key.cmd)
            elif len(part) == 1:
                # Single character key
                keys.add(keyboard.KeyCode.from_char(part))
            else:
                # Try to get special key
                try:
                    key_attr = getattr(keyboard.Key, part, None)
                    if key_attr:
                        keys.add(key_attr)
                except:
                    pass
        
        return keys
    
    def register_toggle_callback(self, callback):
        """Register callback for toggle hotkey"""
        self.hotkey_callbacks['toggle'] = callback
    
    def register_stats_callback(self, callback):
        """Register callback for stats hotkey"""
        self.hotkey_callbacks['stats'] = callback
    
    def start(self):
        """Start listening for hotkeys"""
        if self.is_running:
            return
            
        self.is_running = True
        self.listener = keyboard.Listener(
            on_press=self._on_key_press,
            on_release=self._on_key_release,
            suppress=False
        )
        self.listener.start()
    
    def stop(self):
        """Stop listening for hotkeys"""
        self.is_running = False
        if self.listener:
            self.listener.stop()
            self.listener = None
        self.pressed_keys.clear()
    
    def _on_key_press(self, key):
        """Handle key press events"""
        if not self.is_running:
            return
            
        self.pressed_keys.add(key)
        
        # Check for hotkey combinations
        self._check_hotkeys()
    
    def _on_key_release(self, key):
        """Handle key release events"""
        if not self.is_running:
            return
            
        self.pressed_keys.discard(key)
    
    def _check_hotkeys(self):
        """Check if any registered hotkeys are pressed"""
        # Check toggle hotkey
        if self._is_hotkey_pressed(self.toggle_hotkey):
            callback = self.hotkey_callbacks.get('toggle')
            if callback:
                # Use threading to prevent blocking
                threading.Thread(target=callback, daemon=True).start()
        
        # Check stats hotkey
        if self._is_hotkey_pressed(self.stats_hotkey):
            callback = self.hotkey_callbacks.get('stats')
            if callback:
                # Use threading to prevent blocking
                threading.Thread(target=callback, daemon=True).start()
    
    def _is_hotkey_pressed(self, hotkey_keys):
        """Check if a specific hotkey combination is currently pressed"""
        if not hotkey_keys:
            return False
        
        # For modifier keys, we need to check if any of the variants are pressed
        required_keys = set()
        for key in hotkey_keys:
            if key in [keyboard.Key.ctrl_l, keyboard.Key.ctrl_r]:
                # Check if either ctrl key is pressed
                if keyboard.Key.ctrl_l in self.pressed_keys or keyboard.Key.ctrl_r in self.pressed_keys:
                    required_keys.add('ctrl')
            elif key in [keyboard.Key.shift_l, keyboard.Key.shift_r]:
                # Check if either shift key is pressed
                if keyboard.Key.shift_l in self.pressed_keys or keyboard.Key.shift_r in self.pressed_keys:
                    required_keys.add('shift')
            elif key in [keyboard.Key.alt_l, keyboard.Key.alt_r]:
                # Check if either alt key is pressed
                if keyboard.Key.alt_l in self.pressed_keys or keyboard.Key.alt_r in self.pressed_keys:
                    required_keys.add('alt')
            elif key == keyboard.Key.cmd:
                if keyboard.Key.cmd in self.pressed_keys:
                    required_keys.add('cmd')
            else:
                # Regular key
                if key in self.pressed_keys:
                    required_keys.add(str(key))
        
        # Check if we have the right number of keys pressed
        expected_count = len(hotkey_keys)
        
        # Count unique modifier types
        modifier_count = 0
        regular_key_count = 0
        
        for key in hotkey_keys:
            if key in [keyboard.Key.ctrl_l, keyboard.Key.ctrl_r]:
                if 'ctrl' not in [k for k in required_keys if k in ['ctrl']]:
                    modifier_count += 1
                else:
                    modifier_count = max(modifier_count, 1)
            elif key in [keyboard.Key.shift_l, keyboard.Key.shift_r]:
                if 'shift' not in [k for k in required_keys if k in ['shift']]:
                    modifier_count += 1
                else:
                    modifier_count = max(modifier_count, 1)
            elif key in [keyboard.Key.alt_l, keyboard.Key.alt_r]:
                if 'alt' not in [k for k in required_keys if k in ['alt']]:
                    modifier_count += 1
                else:
                    modifier_count = max(modifier_count, 1)
            elif key == keyboard.Key.cmd:
                modifier_count += 1
            else:
                regular_key_count += 1
        
        # Simple approach: check if all required keys are present
        ctrl_pressed = (keyboard.Key.ctrl_l in self.pressed_keys or 
                       keyboard.Key.ctrl_r in self.pressed_keys)
        shift_pressed = (keyboard.Key.shift_l in self.pressed_keys or 
                        keyboard.Key.shift_r in self.pressed_keys)
        alt_pressed = (keyboard.Key.alt_l in self.pressed_keys or 
                      keyboard.Key.alt_r in self.pressed_keys)
        
        # Check specific combinations
        toggle_keys = self.config.get('toggle_hotkey', '').lower()
        stats_keys = self.config.get('stats_hotkey', '').lower()
        
        if hotkey_keys == self.toggle_hotkey:
            # Default: ctrl+shift+t
            if 'ctrl+shift+t' in toggle_keys:
                return (ctrl_pressed and shift_pressed and 
                       (keyboard.KeyCode.from_char('t') in self.pressed_keys))
        
        if hotkey_keys == self.stats_hotkey:
            # Default: ctrl+shift+s
            if 'ctrl+shift+s' in stats_keys:
                return (ctrl_pressed and shift_pressed and 
                       (keyboard.KeyCode.from_char('s') in self.pressed_keys))
        
        return False


class SimpleHotkeyManager:
    """Simplified hotkey manager with better reliability"""
    def __init__(self, config):
        self.config = config
        self.listener = None
        self.toggle_callback = None
        self.stats_callback = None
        self.is_running = False
        self.last_toggle_time = 0
        self.last_stats_time = 0
        self.debounce_time = 0.5  # 500ms debounce
        
    def register_toggle_callback(self, callback):
        """Register callback for toggle hotkey"""
        self.toggle_callback = callback
    
    def register_stats_callback(self, callback):
        """Register callback for stats hotkey"""
        self.stats_callback = callback
    
    def start(self):
        """Start listening for hotkeys"""
        if self.is_running:
            return
            
        self.is_running = True
        
        # Use pynput's built-in hotkey functionality
        try:
            # Parse hotkey strings
            toggle_hotkey = self.config.get('toggle_hotkey', 'ctrl+shift+t')
            stats_hotkey = self.config.get('stats_hotkey', 'ctrl+shift+s')
            
            # Create hotkey combinations
            self.hotkeys = []
            
            # Toggle hotkey
            if self.toggle_callback:
                try:
                    hotkey = keyboard.HotKey(
                        keyboard.HotKey.parse(toggle_hotkey),
                        self._on_toggle_hotkey
                    )
                    self.hotkeys.append(hotkey)
                except:
                    print(f"Warning: Could not parse toggle hotkey: {toggle_hotkey}")
            
            # Stats hotkey
            if self.stats_callback:
                try:
                    hotkey = keyboard.HotKey(
                        keyboard.HotKey.parse(stats_hotkey),
                        self._on_stats_hotkey
                    )
                    self.hotkeys.append(hotkey)
                except:
                    print(f"Warning: Could not parse stats hotkey: {stats_hotkey}")
            
            # Start listener
            self.listener = keyboard.Listener(
                on_press=self._on_key_press,
                on_release=self._on_key_release,
                suppress=False
            )
            self.listener.start()
            
        except Exception as e:
            print(f"Error starting hotkey manager: {e}")
            self.is_running = False
    
    def stop(self):
        """Stop listening for hotkeys"""
        self.is_running = False
        if self.listener:
            self.listener.stop()
            self.listener = None
        self.hotkeys = []
    
    def _on_key_press(self, key):
        """Handle key press events"""
        if not self.is_running:
            return
            
        for hotkey in self.hotkeys:
            hotkey.press(key)
    
    def _on_key_release(self, key):
        """Handle key release events"""
        if not self.is_running:
            return
            
        for hotkey in self.hotkeys:
            hotkey.release(key)
    
    def _on_toggle_hotkey(self):
        """Handle toggle hotkey activation"""
        current_time = time.time()
        if current_time - self.last_toggle_time > self.debounce_time:
            self.last_toggle_time = current_time
            if self.toggle_callback:
                threading.Thread(target=self.toggle_callback, daemon=True).start()
    
    def _on_stats_hotkey(self):
        """Handle stats hotkey activation"""
        current_time = time.time()
        if current_time - self.last_stats_time > self.debounce_time:
            self.last_stats_time = current_time
            if self.stats_callback:
                threading.Thread(target=self.stats_callback, daemon=True).start()
