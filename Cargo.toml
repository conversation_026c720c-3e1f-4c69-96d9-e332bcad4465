[package]
name = "typer-overlay"
version = "0.1.0"
edition = "2021"
description = "A lightweight typing practice overlay for Windows"
authors = ["Your Name <<EMAIL>>"]

[dependencies]
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
rand = "0.8"
tokio = { version = "1.0", features = ["full"] }
winapi = { version = "0.3", features = [
    "winuser",
    "wingdi",
    "windef",
    "libloaderapi",
    "processthreadsapi",
    "wincon",
    "consoleapi",
    "handleapi",
    "errhandlingapi",
    "memoryapi",
    "sysinfoapi"
] }
windows = { version = "0.52", features = [
    "Win32_Foundation",
    "Win32_System_LibraryLoader",
    "Win32_UI_WindowsAndMessaging",
    "Win32_Graphics_Gdi",
    "Win32_UI_Input_KeyboardAndMouse",
    "Win32_System_Threading",
    "Win32_UI_Shell"
] }
