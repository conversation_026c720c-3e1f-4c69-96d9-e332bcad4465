use std::sync::Arc;
use std::ffi::OsStr;
use std::os::windows::ffi::OsStrExt;
use std::ptr;
use std::mem;
use tokio::sync::Mutex;

use winapi::um::winuser::*;
use winapi::um::wingdi::*;
use winapi::um::libloaderapi::GetModuleHandleW;
use winapi::shared::windef::*;
use winapi::shared::minwindef::*;

use crate::config::Config;
use crate::session_manager::{SessionManager, SessionStatistics};

pub struct StatisticsDisplay {
    config: Arc<Config>,
    session_manager: Arc<Mutex<SessionManager>>,
    hwnd: Option<HWND>,
    hdc: Option<HDC>,
    is_visible: bool,
    window_width: i32,
    window_height: i32,
}

impl StatisticsDisplay {
    pub fn new(
        config: Arc<Config>,
        session_manager: Arc<Mutex<SessionManager>>,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        Ok(StatisticsDisplay {
            config,
            session_manager,
            hwnd: None,
            hdc: None,
            is_visible: false,
            window_width: 300,
            window_height: 400,
        })
    }
    
    pub fn create_window(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        unsafe {
            // Register window class
            let class_name = to_wide_string("TypewriterStatsClass");
            let window_title = to_wide_string("Typing Statistics");
            
            let wc = WNDCLASSEXW {
                cbSize: mem::size_of::<WNDCLASSEXW>() as u32,
                style: CS_HREDRAW | CS_VREDRAW,
                lpfnWndProc: Some(stats_window_proc),
                cbClsExtra: 0,
                cbWndExtra: 0,
                hInstance: GetModuleHandleW(ptr::null()),
                hIcon: ptr::null_mut(),
                hCursor: LoadCursorW(ptr::null_mut(), IDC_ARROW),
                hbrBackground: (COLOR_WINDOW + 1) as HBRUSH,
                lpszMenuName: ptr::null(),
                lpszClassName: class_name.as_ptr(),
                hIconSm: ptr::null_mut(),
            };
            
            if RegisterClassExW(&wc) == 0 {
                let error = GetLastError();
                if error != ERROR_CLASS_ALREADY_EXISTS {
                    return Err(format!("Failed to register stats window class: {}", error).into());
                }
            }
            
            // Create window (positioned in top-right corner)
            let screen_width = GetSystemMetrics(SM_CXSCREEN);
            let x_position = screen_width - self.window_width - 20;
            
            let hwnd = CreateWindowExW(
                WS_EX_TOPMOST | WS_EX_TOOLWINDOW,
                class_name.as_ptr(),
                window_title.as_ptr(),
                WS_OVERLAPPED | WS_CAPTION | WS_SYSMENU | WS_MINIMIZEBOX,
                x_position,
                20,
                self.window_width,
                self.window_height,
                ptr::null_mut(),
                ptr::null_mut(),
                GetModuleHandleW(ptr::null()),
                ptr::null_mut(),
            );
            
            if hwnd.is_null() {
                return Err(format!("Failed to create stats window: {}", GetLastError()).into());
            }
            
            self.hwnd = Some(hwnd);
            
            // Get device context
            let hdc = GetDC(hwnd);
            if hdc.is_null() {
                return Err("Failed to get stats window device context".into());
            }
            self.hdc = Some(hdc);
            
            Ok(())
        }
    }
    
    pub fn show(&mut self) {
        if let Some(hwnd) = self.hwnd {
            unsafe {
                ShowWindow(hwnd, SW_SHOW);
                UpdateWindow(hwnd);
                self.is_visible = true;
            }
        }
    }
    
    pub fn hide(&mut self) {
        if let Some(hwnd) = self.hwnd {
            unsafe {
                ShowWindow(hwnd, SW_HIDE);
                self.is_visible = false;
            }
        }
    }
    
    pub fn toggle_visibility(&mut self) {
        if self.is_visible {
            self.hide();
        } else {
            self.show();
        }
    }
    
    pub fn is_visible(&self) -> bool {
        self.is_visible
    }
    
    pub async fn update(&self) -> Result<(), Box<dyn std::error::Error>> {
        if !self.is_visible {
            return Ok(());
        }
        
        // Get current statistics
        let stats = {
            let session_manager = self.session_manager.lock().await;
            session_manager.get_statistics()
        };
        
        self.render_statistics(&stats)?;
        
        // Process window messages
        if let Some(hwnd) = self.hwnd {
            unsafe {
                let mut msg = MSG {
                    hwnd: ptr::null_mut(),
                    message: 0,
                    wParam: 0,
                    lParam: 0,
                    time: 0,
                    pt: POINT { x: 0, y: 0 },
                };
                
                while PeekMessageW(&mut msg, hwnd, 0, 0, PM_REMOVE) != 0 {
                    TranslateMessage(&msg);
                    DispatchMessageW(&msg);
                }
            }
        }
        
        Ok(())
    }
    
    fn render_statistics(&self, stats: &SessionStatistics) -> Result<(), Box<dyn std::error::Error>> {
        if let Some(hdc) = self.hdc {
            unsafe {
                // Clear the window
                let mut rect = RECT {
                    left: 0,
                    top: 0,
                    right: self.window_width,
                    bottom: self.window_height,
                };
                
                let bg_brush = CreateSolidBrush(RGB(240, 240, 240));
                FillRect(hdc, &rect, bg_brush);
                DeleteObject(bg_brush as *mut _);
                
                // Set up text drawing
                SetBkMode(hdc, TRANSPARENT);
                
                // Create font
                let font = CreateFontW(
                    -16, 0, 0, 0, FW_NORMAL, 0, 0, 0,
                    DEFAULT_CHARSET, OUT_DEFAULT_PRECIS, CLIP_DEFAULT_PRECIS,
                    DEFAULT_QUALITY, DEFAULT_PITCH | FF_DONTCARE,
                    to_wide_string("Segoe UI").as_ptr(),
                );
                let old_font = SelectObject(hdc, font as *mut _);
                
                let mut y_pos = 20;
                let line_height = 25;
                
                // Status
                let status_text = if stats.active { "Status: Active" } else { "Status: Inactive" };
                let status_color = if stats.active { RGB(0, 128, 0) } else { RGB(128, 128, 128) };
                self.draw_text_line(hdc, status_text, 20, y_pos, status_color);
                y_pos += line_height;
                
                // Duration
                let duration_minutes = (stats.duration / 60.0) as i32;
                let duration_seconds = (stats.duration % 60.0) as i32;
                let duration_text = format!("Duration: {}:{:02}", duration_minutes, duration_seconds);
                self.draw_text_line(hdc, &duration_text, 20, y_pos, RGB(0, 0, 0));
                y_pos += line_height;
                
                // WPM
                let wpm_text = format!("WPM: {:.1}", stats.wpm);
                self.draw_text_line(hdc, &wpm_text, 20, y_pos, RGB(0, 0, 0));
                y_pos += line_height;
                
                // Accuracy
                let accuracy_color = self.get_accuracy_color(stats.accuracy);
                let accuracy_text = format!("Accuracy: {:.1}%", stats.accuracy);
                self.draw_text_line(hdc, &accuracy_text, 20, y_pos, accuracy_color);
                y_pos += line_height * 2;
                
                // Detailed statistics
                self.draw_text_line(hdc, "--- Session Details ---", 20, y_pos, RGB(0, 0, 0));
                y_pos += line_height;
                
                let keystrokes_text = format!("Total Keystrokes: {}", stats.total_keystrokes);
                self.draw_text_line(hdc, &keystrokes_text, 20, y_pos, RGB(0, 0, 0));
                y_pos += line_height;
                
                let correct_text = format!("Correct: {}", stats.correct_keystrokes);
                self.draw_text_line(hdc, &correct_text, 20, y_pos, RGB(0, 128, 0));
                y_pos += line_height;
                
                let incorrect_text = format!("Incorrect: {}", stats.incorrect_keystrokes);
                self.draw_text_line(hdc, &incorrect_text, 20, y_pos, RGB(128, 0, 0));
                y_pos += line_height;
                
                let backspaces_text = format!("Backspaces: {}", stats.backspaces);
                self.draw_text_line(hdc, &backspaces_text, 20, y_pos, RGB(128, 128, 0));
                y_pos += line_height;
                
                let words_text = format!("Words Completed: {}", stats.words_completed);
                self.draw_text_line(hdc, &words_text, 20, y_pos, RGB(0, 0, 128));
                
                // Cleanup
                SelectObject(hdc, old_font);
                DeleteObject(font as *mut _);
            }
        }
        
        Ok(())
    }
    
    fn draw_text_line(&self, hdc: HDC, text: &str, x: i32, y: i32, color: COLORREF) {
        unsafe {
            SetTextColor(hdc, color);
            let wide_text = to_wide_string(text);
            TextOutW(hdc, x, y, wide_text.as_ptr(), wide_text.len() as i32 - 1);
        }
    }
    
    fn get_accuracy_color(&self, accuracy: f64) -> COLORREF {
        if accuracy >= 95.0 {
            RGB(0, 128, 0) // Green
        } else if accuracy >= 85.0 {
            RGB(255, 165, 0) // Orange
        } else {
            RGB(255, 0, 0) // Red
        }
    }
}

impl Drop for StatisticsDisplay {
    fn drop(&mut self) {
        unsafe {
            if let Some(hdc) = self.hdc {
                if let Some(hwnd) = self.hwnd {
                    ReleaseDC(hwnd, hdc);
                }
            }
            
            if let Some(hwnd) = self.hwnd {
                DestroyWindow(hwnd);
            }
        }
    }
}

pub struct StatisticsTracker {
    session_history: Vec<SessionStatistics>,
    total_sessions: u32,
    total_time: f64,
    total_words: u32,
    total_keystrokes: u32,
    best_wpm: f64,
    best_accuracy: f64,
}

impl StatisticsTracker {
    pub fn new() -> Self {
        StatisticsTracker {
            session_history: Vec::new(),
            total_sessions: 0,
            total_time: 0.0,
            total_words: 0,
            total_keystrokes: 0,
            best_wpm: 0.0,
            best_accuracy: 0.0,
        }
    }
    
    pub fn add_session(&mut self, session_stats: SessionStatistics) {
        self.session_history.push(session_stats.clone());
        self.total_sessions += 1;
        self.total_time += session_stats.duration;
        self.total_words += session_stats.words_completed;
        self.total_keystrokes += session_stats.total_keystrokes;
        
        // Update best scores
        if session_stats.wpm > self.best_wpm {
            self.best_wpm = session_stats.wpm;
        }
        
        if session_stats.accuracy > self.best_accuracy {
            self.best_accuracy = session_stats.accuracy;
        }
        
        // Keep only last 100 sessions to prevent memory bloat
        if self.session_history.len() > 100 {
            self.session_history.remove(0);
        }
    }
    
    pub fn get_overall_statistics(&self) -> OverallStatistics {
        if self.total_sessions == 0 {
            return OverallStatistics::default();
        }
        
        // Calculate averages from recent sessions
        let recent_sessions: Vec<_> = self.session_history.iter().rev().take(20).collect();
        let (avg_wpm, avg_accuracy) = if !recent_sessions.is_empty() {
            let total_wpm: f64 = recent_sessions.iter().map(|s| s.wpm).sum();
            let total_accuracy: f64 = recent_sessions.iter().map(|s| s.accuracy).sum();
            (total_wpm / recent_sessions.len() as f64, total_accuracy / recent_sessions.len() as f64)
        } else {
            (0.0, 0.0)
        };
        
        OverallStatistics {
            total_sessions: self.total_sessions,
            total_time: self.total_time,
            average_wpm: avg_wpm,
            average_accuracy: avg_accuracy,
            best_wpm: self.best_wpm,
            best_accuracy: self.best_accuracy,
            total_words: self.total_words,
            total_keystrokes: self.total_keystrokes,
        }
    }
}

#[derive(Debug, Clone)]
pub struct OverallStatistics {
    pub total_sessions: u32,
    pub total_time: f64,
    pub average_wpm: f64,
    pub average_accuracy: f64,
    pub best_wpm: f64,
    pub best_accuracy: f64,
    pub total_words: u32,
    pub total_keystrokes: u32,
}

impl Default for OverallStatistics {
    fn default() -> Self {
        OverallStatistics {
            total_sessions: 0,
            total_time: 0.0,
            average_wpm: 0.0,
            average_accuracy: 0.0,
            best_wpm: 0.0,
            best_accuracy: 0.0,
            total_words: 0,
            total_keystrokes: 0,
        }
    }
}

// Helper functions
fn to_wide_string(s: &str) -> Vec<u16> {
    OsStr::new(s).encode_wide().chain(Some(0)).collect()
}

unsafe extern "system" fn stats_window_proc(
    hwnd: HWND,
    msg: UINT,
    wparam: WPARAM,
    lparam: LPARAM,
) -> LRESULT {
    match msg {
        WM_PAINT => {
            let mut ps = PAINTSTRUCT {
                hdc: ptr::null_mut(),
                fErase: 0,
                rcPaint: RECT { left: 0, top: 0, right: 0, bottom: 0 },
                fRestore: 0,
                fIncUpdate: 0,
                rgbReserved: [0; 32],
            };
            
            let hdc = BeginPaint(hwnd, &mut ps);
            // Custom painting is handled in render_statistics
            EndPaint(hwnd, &ps);
            0
        }
        WM_CLOSE => {
            ShowWindow(hwnd, SW_HIDE);
            0 // Don't destroy, just hide
        }
        _ => DefWindowProcW(hwnd, msg, wparam, lparam),
    }
}
